{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\SavedReports\\SavedReports.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\SavedReports\\SavedReports.vue", "mtime": 1756316146027}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfSBmcm9tICIuL1NhdmVkUmVwb3J0cy52dWU/dnVlJnR5cGU9dGVtcGxhdGUmaWQ9ZmZlNmU2ZjYmc2NvcGVkPXRydWUiCmltcG9ydCBzY3JpcHQgZnJvbSAiLi9TYXZlZFJlcG9ydHMudnVlP3Z1ZSZ0eXBlPXNjcmlwdCZsYW5nPWpzIgpleHBvcnQgKiBmcm9tICIuL1NhdmVkUmVwb3J0cy52dWU/dnVlJnR5cGU9c2NyaXB0Jmxhbmc9anMiCmltcG9ydCBzdHlsZTAgZnJvbSAiLi9TYXZlZFJlcG9ydHMudnVlP3Z1ZSZ0eXBlPXN0eWxlJmluZGV4PTAmaWQ9ZmZlNmU2ZjYmc2NvcGVkPXRydWUmbGFuZz1zY3NzIgoKCi8qIG5vcm1hbGl6ZSBjb21wb25lbnQgKi8KaW1wb3J0IG5vcm1hbGl6ZXIgZnJvbSAiIS4uLy4uLy4uL25vZGVfbW9kdWxlcy92dWUtbG9hZGVyL2xpYi9ydW50aW1lL2NvbXBvbmVudE5vcm1hbGl6ZXIuanMiCnZhciBjb21wb25lbnQgPSBub3JtYWxpemVyKAogIHNjcmlwdCwKICByZW5kZXIsCiAgc3RhdGljUmVuZGVyRm5zLAogIGZhbHNlLAogIG51bGwsCiAgImZmZTZlNmY2IiwKICBudWxsCiAgCikKCi8qIGhvdCByZWxvYWQgKi8KaWYgKG1vZHVsZS5ob3QpIHsKICB2YXIgYXBpID0gcmVxdWlyZSgiQzpcXFVzZXJzXFxEYW5pZWxSdWl6MVxcRG9jdW1lbnRzXFxzdGF0aXRfMlxcY2xpZW50XFxub2RlX21vZHVsZXNcXHZ1ZS1ob3QtcmVsb2FkLWFwaVxcZGlzdFxcaW5kZXguanMiKQogIGFwaS5pbnN0YWxsKHJlcXVpcmUoJ3Z1ZScpKQogIGlmIChhcGkuY29tcGF0aWJsZSkgewogICAgbW9kdWxlLmhvdC5hY2NlcHQoKQogICAgaWYgKCFhcGkuaXNSZWNvcmRlZCgnZmZlNmU2ZjYnKSkgewogICAgICBhcGkuY3JlYXRlUmVjb3JkKCdmZmU2ZTZmNicsIGNvbXBvbmVudC5vcHRpb25zKQogICAgfSBlbHNlIHsKICAgICAgYXBpLnJlbG9hZCgnZmZlNmU2ZjYnLCBjb21wb25lbnQub3B0aW9ucykKICAgIH0KICAgIG1vZHVsZS5ob3QuYWNjZXB0KCIuL1NhdmVkUmVwb3J0cy52dWU/dnVlJnR5cGU9dGVtcGxhdGUmaWQ9ZmZlNmU2ZjYmc2NvcGVkPXRydWUiLCBmdW5jdGlvbiAoKSB7CiAgICAgIGFwaS5yZXJlbmRlcignZmZlNmU2ZjYnLCB7CiAgICAgICAgcmVuZGVyOiByZW5kZXIsCiAgICAgICAgc3RhdGljUmVuZGVyRm5zOiBzdGF0aWNSZW5kZXJGbnMKICAgICAgfSkKICAgIH0pCiAgfQp9CmNvbXBvbmVudC5vcHRpb25zLl9fZmlsZSA9ICJzcmMvdmlld3MvU2F2ZWRSZXBvcnRzL1NhdmVkUmVwb3J0cy52dWUiCmV4cG9ydCBkZWZhdWx0IGNvbXBvbmVudC5leHBvcnRz"}]}