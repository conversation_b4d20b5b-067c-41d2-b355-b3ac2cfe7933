{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1B\\Phase1B.vue?vue&type=template&id=4c478242&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1B\\Phase1B.vue", "mtime": 1757086169836}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}