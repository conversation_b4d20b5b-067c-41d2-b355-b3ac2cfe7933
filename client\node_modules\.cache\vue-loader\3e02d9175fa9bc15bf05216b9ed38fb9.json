{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1B\\Phase1B.vue?vue&type=template&id=4c478242&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1B\\Phase1B.vue", "mtime": 1757092121550}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}