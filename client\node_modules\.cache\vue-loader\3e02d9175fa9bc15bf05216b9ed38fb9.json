{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1B\\Phase1B.vue?vue&type=template&id=4c478242&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1B\\Phase1B.vue", "mtime": 1757108602483}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}