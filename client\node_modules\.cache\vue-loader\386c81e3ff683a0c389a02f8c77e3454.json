{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\SavedReports\\SavedReports.vue?vue&type=template&id=ffe6e6f6&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\SavedReports\\SavedReports.vue", "mtime": 1756316146027}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}