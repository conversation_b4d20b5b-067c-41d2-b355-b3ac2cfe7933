<style scoped>
.short-dropdown {
  max-width: 200px; /* Adjust the width as needed */
}

.submit-button {
  margin-top: 20px; /* Add some spacing above the button */
}

.flex-container {
  display: flex;
  align-items: flex-start; /* Align items at the top of the container */
  gap: 20px; /* Space between dropdowns and the chart */
}

.left-column {
  margin-top:15px;
  flex: 1; /* Take up 1 part of the space */
  max-width: 300px; /* Optional: Control max width of the left column */
}

.right-column {
  flex: 3; /* Take up 3 parts of the space */
}

.ai-query-section {
  margin-bottom: 20px;
  padding: 15px;
  /* border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f4f4f4; */
}

.ai-query-label {
  font-weight: 600;
  margin-bottom: 10px;
  color: #e3e3e3;
}

.ai-textarea {
  width: 100%;
  margin-bottom: 15px;
}

.ai-buttons {
  display: flex;
  gap: 10px;
}

.query-method-toggle {
  margin-bottom: 20px;
}

.manual-controls {
  margin-top: 15px;
}
</style>

<template>
  
  <cv-grid>
    <template>
    <MainHeader :expandedSideNav=false :useFixed="useFixed" />
    <div class="left-column">
      <h1>Phase 1</h1>

      <!-- Query Method Toggle -->
      <div class="query-method-toggle">
        <cv-radio-group 
          v-model="queryMethod" 
          @change="handleQueryMethodChange"
          label="Query Method"
          
        >
          <cv-radio-button 
            name="queryMethod" 
            label="Natural Language Query" 
            value="ai"
          />
          <cv-radio-button 
            name="queryMethod" 
            label="Manual Selection" 
            value="manual"
          />
        </cv-radio-group>
      </div>

      <!-- AI Query Section -->
      <div v-if="queryMethod === 'ai'" class="ai-query-section">
        <div class="ai-query-label">What would you like to view?</div>
        <cv-text-area
          v-model="aiQuery"
          placeholder="Example: Show me bar chart for PN ABC123 in area XYZ for category defects from 2024-01-01 to 2024-03-31"
          rows="4"
          class="ai-textarea"
        />
        <div class="ai-buttons">
          <cv-button 
            @click="processAiQuery" 
            :disabled="loading || !aiQuery.trim()"
            kind="primary"
          >
            {{ loading ? 'Processing...' : 'Generate Query' }}
          </cv-button>
          <cv-button 
            @click="clearAiQuery" 
            kind="secondary"
          >
            Clear
          </cv-button>
        </div>
      </div>

      <!-- Manual Controls Section -->
      <div v-if="queryMethod === 'manual'" class="manual-controls">
        <cv-radio-group 
          v-model="queryType" 
          @change="handleQueryTypeChange"
          label="Query Type"
          
        >
          <cv-radio-button 
            name="queryType" 
            label="Fab/Ful DefRate" 
            value="fabful"
          />
          <cv-radio-button 
            name="queryType" 
            label="Pareto" 
            value="pareto"
          />
          <cv-radio-button 
            name="queryType" 
            label="Compare" 
            value="compare"
          />
        </cv-radio-group>
        <div v-if="queryType === 'fabful'" >
              

              <div class="chart-controls">
                <div class="control-group">
                  <label class="control-label">View By:</label>
                  <cv-dropdown
                    v-model="rootCauseViewBy"
                    @change="handleRootCauseViewByChange"
                    class="control-dropdown"
                  >
                    <cv-dropdown-item value="rootCause">Root Cause</cv-dropdown-item>
                    <cv-dropdown-item value="vintage">Vintage</cv-dropdown-item>
                    <cv-dropdown-item value="sector">Sector</cv-dropdown-item>
                    <cv-dropdown-item value="supplier">Supplier</cv-dropdown-item>
                    <cv-dropdown-item value="partNum">Part Number</cv-dropdown-item>
                  </cv-dropdown>
                </div>

                <div class="control-group">
                  <label class="control-label">Time Range:</label>
                  <cv-dropdown
                    v-model="rootCauseTimeRange"
                    @change="handleRootCauseTimeRangeChange"
                    class="control-dropdown"
                  >
                    <cv-dropdown-item value="3month">3 Months</cv-dropdown-item>
                    <cv-dropdown-item value="6month">6 Months</cv-dropdown-item>
                  </cv-dropdown>
                </div>

                <div class="control-group" v-if="breakoutGroups.length > 0">
                  <label class="control-label">Group:</label>
                  <cv-dropdown
                    v-model="rootCauseSelectedGroup"
                    @change="handleRootCauseGroupChange"
                    class="control-dropdown"
                  >
                    <cv-dropdown-item value="all">All Groups</cv-dropdown-item>
                    <cv-dropdown-item
                      v-for="group in breakoutGroups"
                      :key="group.name"
                      :value="group.name"
                    >
                      {{ group.name }}
                    </cv-dropdown-item>
                  </cv-dropdown>
                </div>
              </div>

              <div class="chart-container">
                <div v-if="isRootCauseDataLoading" >
                          Loading Chart...
                          <RootCauseChart :data = [] :loading="isRootCauseDataLoading"/>
                        </div>
                <RootCauseChart
                 v-if="rootCauseChartData.length > 0 && !isRootCauseDataLoading"
                  :data="rootCauseChartData"
                  :viewBy="rootCauseViewBy"
                  :timeRange="rootCauseTimeRange"
                  :selectedGroup="rootCauseSelectedGroup"
                  :loading="isRootCauseDataLoading"
                  :title = "rootCauseTitle"
                  @bar-click="handleRootCauseBarClick"
                />

                <div v-if="rootCauseChartData.length == 0 && !isRootCauseDataLoading" >
                          No data available
                </div>
              </div>
            
              
      </div>
        <div v-if="queryType === 'pareto'" >
        
        
        <!-- PN Dropdown -->
        <cv-combo-box class="short-dropdown"
          v-model="selectedPN"
          label="PN"
          :options="pnOptions"
        ></cv-combo-box>

        <!-- Area Dropdown -->
        <cv-dropdown class="short-dropdown"
          v-model="selectedArea"
          label="Area"
          :items="areaOptions"
        ></cv-dropdown>

        <!-- Category Dropdown -->
        <cv-dropdown class="short-dropdown"
          v-model="selectedCategory"
          label="Category"
          :items="categoryOptions"
        ></cv-dropdown>

         <!-- Date Select -->
         <cv-date-picker
          v-model="selectedDateRange"
          label="Choose Date Range"
          kind = 'range'
          :cal-options = "calOptions"
          placeholder = "yyyy-mm-dd"
        ></cv-date-picker>

        <!-- Chart Type Dropdown -->
        <cv-dropdown class="short-dropdown"
          v-model="selectedChartType"
          label="Chart Type"
          :items="chartTypeOptions"
        ></cv-dropdown>

        <!-- Submit Button -->
        <cv-button @click="handleSubmit" class="submit-button">
          Submit
        </cv-button>
      </div>
      </div>

    </div>

    <div class="right-column">
      <cv-tile :light="lightTile">
        <!-- Show loading skeletons while data is being fetched -->
        <div v-if="loading && displayChartType === 'Bar'">
          Loading Bar Chart...
          <BarChart :data = [] :loading="loading"/>
        </div>
        <div v-if="loading && displayChartType === 'Line'" >
          Loading Line Chart...
          <LineChart :data = [] :loading="loading"/>
        </div>

        <!-- Show charts after data is loaded -->
        <BarChart v-if="displayChartType === 'Bar' && bar_chart_data.length > 0" :data="bar_chart_data" @bar-clicked="handleBarClick" :loading="loading"/>
        <LineChart v-if="displayChartType === 'Line' && line_chart_data.length > 0" :data="line_chart_data" @point-clicked="handlePointClick" :loading="loading"/>
      </cv-tile>
    </div>
  </template>
  </cv-grid>
</template>



<script>
import LineChart from '../../components/LineChart'; // Import the LineChart component
import BarChart from '../../components/BarChart'; //import barchart
import MainHeader from '../../components/MainHeader';
import RootCauseChart from '@/components/RootCauseChart/RootCauseChart';

export default {
  name: 'Phase1B',
  components: {
    LineChart,
    BarChart,
    RootCauseChart,
    MainHeader
  },
  data() {
    return {
      queryMethod: 'manual', // Default to AI query method
      queryType: 'fabful', // Default to Fab/Ful DefRate
      aiQuery: '',
      selectedPN: "",
      selectedArea: "",
      selectedStartDate: "",
      selectedEndDate: "",
      selectedDateRange: "",
      selectedCategory: "",
      selectedChartType: "",
      pnOptions: [],
      areaOptions: [],
      categoryOptions: [],
      chartTypeOptions: ['Bar', 'Line'],
      line_chart_data: [],
      bar_chart_data: [],
      displayChartType: "",
      chartData: [], 
      lightTile:true,
      loading: false,
      calOptions: {dateFormat: "Y-m-d"},

      // Root Cause Chart Data
      rootCauseChartData: [],
      rootCauseTitle: '',
      isRootCauseDataLoading: true,

      // Breakout Groups Data
      breakoutGroups: [],


      // Root Cause Chart Controls
      rootCauseViewBy: 'rootCause',
      rootCauseTimeRange: '6month',
      rootCauseSelectedGroup: 'all',
    };
  },
  mounted() {
    this.get_PNs();
  },
  watch: {
  selectedPN(newPN) {
    if (newPN) {
      this.get_categories();
    }
  },
  selectedArea(newArea) {
    if (newArea) {
      this.get_categories2("area");
      console.log("DAN1")
    }
  },
  selectedCategory(newCategory) {
    if (newCategory) {
      this.get_categories2("category");
    }
  }
},
  methods: {
    async processAiQuery() {
      if (!this.aiQuery.trim()) return;
      
      this.loading = true;
      
      try {
        const token = this.$store.getters.getToken;
        
        // Create a prompt for WatsonX.ai to extract parameters
        const prompt = `
You are a data analysis assistant. Extract the following parameters from the user's natural language query for a manufacturing analysis system:

User Query: "${this.aiQuery}"

Please extract and return ONLY a JSON object with these fields:
- pn: Part number without leading zeros (string, empty if not specified). Example: if user says "02EA657", return "02EA657"
- area: Area/stage. Must be one of: FULL, FAB, FASM, or TEAR (string, empty if not specified)
- category: Category/root cause like MECH, ELEC, etc. (string, empty if not specified)
- startDate: Start date in YYYY-MM-DD format (string, empty if not specified) *for example, May 1 2024 is 2024-05-01
- endDate: End date in YYYY-MM-DD format (string, empty if not specified) *for example, May 1 2024 is 2024-05-01
- chartType: Either "Bar" or "Line" (string, default to "Bar" if not specified)

Examples:
- "Show me bar chart for PN 02EA657 in area FULL for category MECH from 2024-06-04 to 2024-06-25"
  Should return: {"pn": "02EA657", "area": "FULL", "category": "MECH", "startDate": "2024-06-04", "endDate": "2024-06-25", "chartType": "Bar"}
 ***THIS IS JUST AN EXAMPLE, DO NOT USE ANY OF THESE VALUES IN THE RESPONSE***

 Return only the JSON object, no other text.
`;

        // Call WatsonX.ai API
        const response = await fetch(process.env.VUE_APP_API_PATH + "watsonx_prompt", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({
            model_id: 'ibm/granite-13b-instruct-v2',
            prompt: prompt,
            temperature: 0.3,
            api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',
            project_id: 'edd72256-56ff-4595-bf4a-350ab686660c'
          }),
        });

        const data = await this.handleResponse(response);
        
        if (data && data.status === 'success') {
          try {
            // Parse the AI response to extract parameters
            const aiResponse = data.generated_text.trim();
            console.log("AI Response:", aiResponse);
            
            // Try to extract JSON from the response
            let extractedParams;
            try {
              extractedParams = JSON.parse(aiResponse);
            } catch (e) {
              // If direct parsing fails, try to find JSON in the response
              const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
              if (jsonMatch) {
                extractedParams = JSON.parse(jsonMatch[0]);
              } else {
                throw new Error("No valid JSON found in AI response");
              }
            }
            
            // Apply the extracted parameters with proper formatting
            if (extractedParams.pn) {
              // Ensure part number has proper leading zeros (10 digits total)
              const pn = extractedParams.pn
              this.selectedPN = pn;
            }
            if (extractedParams.area) this.selectedArea = extractedParams.area;
            if (extractedParams.category) this.selectedCategory = extractedParams.category;
            if (extractedParams.chartType) this.selectedChartType = extractedParams.chartType;

            if (extractedParams.startDate && extractedParams.endDate) {
              // Convert date format if needed (from MM-DD-YYYY to YYYY-MM-DD)
              let startDate = extractedParams.startDate;
              let endDate = extractedParams.endDate;

              // Check if dates are in MM-DD-YYYY format and convert to YYYY-MM-DD
              if (startDate.match(/^\d{2}-\d{2}-\d{4}$/)) {
                const [month, day, year] = startDate.split('-');
                startDate = `${year}-${month}-${day}`;
              }
              if (endDate.match(/^\d{2}-\d{2}-\d{4}$/)) {
                const [month, day, year] = endDate.split('-');
                endDate = `${year}-${month}-${day}`;
              }

              this.selectedDateRange = {
                startDate: startDate,
                endDate: endDate
              };
            }

            console.log("Applied parameters:", {
              PN: this.selectedPN,
              Area: this.selectedArea,
              Category: this.selectedCategory,
              ChartType: this.selectedChartType,
              DateRange: this.selectedDateRange
            });
            console.log("This is the AIQ:,", this.aiQuery);
            // Automatically execute the query
            this.handleSubmit();
            
            
          } catch (error) {
            console.error("Error parsing AI response:", error);
            alert("Sorry, I couldn't understand your query. Please try rephrasing it or use manual selection.");
          }
        }
      } catch (error) {
        console.error("Error processing AI query:", error);
        alert("Error processing your query. Please try again.");
      } finally {
        this.loading = false;
      }
    },
    
    clearAiQuery() {
      this.aiQuery = '';
    },

    clearResults() {
      this.bar_chart_data = [];
      this.line_chart_data = [];
      this.displayChartType = "";
    },

    transformFullDataToChartFormat(fullData) {
      // Transform the full data format to chart format
      if (!fullData || !Array.isArray(fullData)) {
        return [];
      }

      return fullData.map(item => ({
        key: item.YEAR_MONTH,
        value: item.DEFECT_COUNT,
        group: "Defect Count",
        // Keep the full data for reference
        fullData: item
      }));
    },


        // Root Cause Chart methods
    async loadRootCauseData() {
      console.log('Loading root cause chart data for PQE Owner Dashboard');
      this.isRootCauseDataLoading = true;

      try {
        // Get authentication config
        const config = this.getAuthConfig();
        this.pqeOwner = "Albert G";
        // Calculate date range based on selected time range
        const endDate = new Date();
        const startDate = new Date();
        const monthsToFetch = this.rootCauseTimeRange === '3month' ? 2 : 5; // 3 or 6 months including current
        startDate.setMonth(endDate.getMonth() - monthsToFetch);

        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;
        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;

        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}, Group: ${this.rootCauseSelectedGroup}`);

        // Call the new API endpoint
        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: this.pqeOwner,
            startDate: startDateStr,
            endDate: endDateStr,
            selectedGroup: this.rootCauseSelectedGroup === 'all' ? null : this.rootCauseSelectedGroup,
            viewBy: this.rootCauseViewBy
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          // Transform the API data to chart format
          if (this.rootCauseViewBy === "rootCause") {
            this.rootCauseTitle = 'Root Cause Analysis';
          } else if (this.rootCauseViewBy === "vintage") {
            this.rootCauseTitle = 'Vintage Code Analysis';
          } else if (this.rootCauseViewBy === "sector") {
            this.rootCauseTitle = 'Sector Analysis';
          } else if (this.rootCauseViewBy === "supplier") {
            this.rootCauseTitle = 'Supplier Code Analysis';
          } else if (this.rootCauseViewBy === "partNum") {
            this.rootCauseTitle = 'Part Number Analysis';
          }
          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);
          console.log('Root cause chart data loaded:', data.categoryData);
          console.log('Chart data length:', this.rootCauseChartData.length);
          console.log('Sample data point:', this.rootCauseChartData[0]);
          console.log('Breakout groups:', data.breakoutGroups);
          console.log('Part numbers:', data.partNumbers);

          console.log('Chart data loaded, watcher should handle update');
        } else {
          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);
          // Fall back to mock data
          this.rootCauseChartData = this.generateMockRootCauseData();
          console.log('Using mock data:', this.rootCauseChartData.length);
        }
      } catch (error) {
        console.error('Error loading root cause data:', error);
        // Fall back to mock data
        this.rootCauseChartData = this.generateMockRootCauseData();
      } finally {
        this.isRootCauseDataLoading = false;
      }
    },

    handleRootCauseBarClick(event) {
      if (event && event.data) {
        const clickedData = event.data;
        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);

        // You can implement additional functionality here, such as showing more details
        // or navigating to the MetisXFactors Group tab with this specific category
        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\nFail Rate: ${clickedData.value}%`);
      }
    },

    transformRootCauseData(categoryData) {
      // Transform the API response into chart format
      const chartData = [];

      // categoryData structure: { "category": { "2024-03": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }
      for (const [category, monthData] of Object.entries(categoryData)) {
        // Clean up category name (trim whitespace)
        const cleanCategory = category.trim();
        console.log("trim cat", cleanCategory)

        for (const [month, data] of Object.entries(monthData)) {
          chartData.push({
            group: cleanCategory,
            key: month,
            value: parseFloat(data.failRate.toFixed(2))
          });
        }
      }

      console.log('Transformed root cause data:', chartData);
      console.log('Unique groups in data:', [...new Set(chartData.map(d => d.group))]);
      console.log('Unique keys in data:', [...new Set(chartData.map(d => d.key))]);
      console.log('Value range:', Math.min(...chartData.map(d => d.value)), 'to', Math.max(...chartData.map(d => d.value)));
      return chartData;
    },

    generateMockRootCauseData() {
      // Use the actual breakout groups for this PQE owner instead of generic categories
      let categories = [];

      if (this.breakoutGroups && this.breakoutGroups.length > 0) {
        // Use the actual breakout groups for this PQE owner
        categories = this.breakoutGroups.map(group => group.name);

        // If a specific group is selected, filter to only that group
        if (this.rootCauseSelectedGroup !== 'all') {
          categories = categories.filter(category => category === this.rootCauseSelectedGroup);
        }
      } else {
        // Fallback to sample data if breakout groups aren't loaded yet
        if (this.pqeOwner === 'Albert G.') {
          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];
        } else if (this.pqeOwner === 'Sarah L.') {
          categories = ['Stellar Core', 'Nebula Drive'];
        } else {
          categories = ['Sample Group 1', 'Sample Group 2'];
        }

        // If a specific group is selected, filter to only that group
        if (this.rootCauseSelectedGroup !== 'all') {
          categories = categories.filter(category => category === this.rootCauseSelectedGroup);
        }
      }

      // Generate months based on selected time range
      const now = new Date();
      const months = [];
      const monthsToGenerate = this.rootCauseTimeRange === '3month' ? 3 : 6;

      for (let i = monthsToGenerate - 1; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        months.push(`${year}-${month}`);
      }

      const data = [];

      months.forEach(month => {
        categories.forEach(category => {
          // Generate realistic fail rate data (0-5% range)
          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%
          const variation = (Math.random() - 0.5) * 1; // ±0.5%
          const failRate = Math.max(0.1, baseRate + variation);

          data.push({
            group: category,
            key: month,
            value: parseFloat(failRate.toFixed(2))
          });
        });
      });

      return data;
    },

    async load_line_chart() {
      try {

        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_fail_count_by_category_db", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ PN: this.selectedPN, category: this.selectedCategory, area: this.selectedArea }),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          // Use full_data if available (Phase1B format), otherwise use counts_by_period (legacy format)
          if (data.full_data && data.full_data.length > 0) {
            console.log("Using full data format:", data.full_data);
            // Transform full data to chart format
            this.line_chart_data = this.transformFullDataToChartFormat(data.full_data);
          } else {
            console.log("Using legacy counts_by_period format:", data.counts_by_period);
            this.line_chart_data = data.counts_by_period;
          }
          console.log("Line chart data:", this.line_chart_data);
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }finally {
        this.loading = false;  // Set loading to false once data is loaded
      }
    },

    async load_bar_chart() {
      try {
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_fail_count_by_category_db", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({PN: this.selectedPN, category: this.selectedCategory, area: this.selectedArea, startDate: this.selectedStartDate, endDate: this.selectedEndDate}),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          // Use full_data if available (Phase1B format), otherwise use counts_by_period (legacy format)
          if (data.full_data && data.full_data.length > 0) {
            console.log("Using full data format for bar chart:", data.full_data);
            // Transform full data to chart format
            const transformedData = this.transformFullDataToChartFormat(data.full_data);
            this.bar_chart_data = transformedData;
            this.line_chart_data = transformedData;
          } else {
            console.log("Using legacy counts_by_period format for bar chart:", data.counts_by_period);
            this.bar_chart_data = data.counts_by_period;
            this.line_chart_data = data.counts_by_period;
          }
          console.log("Bar chart data:", this.bar_chart_data);
        }
      } catch (error) {
        console.error("Error loading data:", error);
      }finally {
        this.loading = false;  // Set loading to false once data is loaded
      }
    },

    async get_categories() {
      try {
        let user_type = this.$store.getters.getUser_type;
        let action = "view";
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_unique_categories_db", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ "PN": this.selectedPN, user_type, action }),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.categoryOptions = data.all_categories;
          this.areaOptions = data.all_stages;
          console.log(this.areaOptions)
          console.log("Received data:", data);

        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },

    async get_PNs() {
      try {
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_unique_pns", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({}),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          this.pnOptions = data.all_pns;

        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },

    async get_categories2(changedVar) {
      try {
        let user_type = this.$store.getters.getUser_type;
        let action = "view";
        let area = null;
        let category = null;

        if (changedVar === "area"){
          area = this.selectedArea
        }else if (changedVar === "category"){
          category = this.selectedCategory
        }
        let token = this.$store.getters.getToken;
        console.log("TOKEN", token)
        // Fetch data from the API
        const response = await fetch(process.env.VUE_APP_API_PATH + "get_unique_categories_db2", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: "Bearer " + token,
          },
          body: JSON.stringify({ "PN": this.selectedPN, "area": area, "category": category, user_type, action }),
        });

        if (response.status === 401) {
          console.error("Unauthorized: Check your token or credentials.");
        }

        const data = await this.handleResponse(response);

        if (data.status_res === "success") {
          if (changedVar === "area"){
            this.categoryOptions = data.new_array;
          } else if (changedVar === "category"){
            this.areaOptions = data.new_array;
          }

          console.log("Received data:", data);

        }
      } catch (error) {
        console.error("Error loading data:", error);
      }
    },

    handleRootCauseViewByChange() {
      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);
      this.loadRootCauseData();
    },

    handleRootCauseTimeRangeChange() {
      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);
      this.loadRootCauseData();
    },

    handleRootCauseGroupChange() {
      console.log(`Root cause group changed to: ${this.rootCauseSelectedGroup}`);
      console.log('Current chart data length before reload:', this.rootCauseChartData.length);
      this.loadRootCauseData();
    },

    getAuthConfig() {
      // Get authentication token from localStorage or Vuex store
      const token = localStorage.getItem('token') || this.$store.state.token;

      return {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };
    },

    handleResponse(response) {
      if (!response.ok) {
        if (response.status === 401) {
          this.session_expired_visible = true;
        }
      } else {
        return response.json();
      }
    },

    handleBarClick(data) {
      // Update the chart data to only show the clicked bar
      console.log("Bar data received:", data);
      // this.bar_chart_data = [data];
    },

    handlePointClick(data) {
      console.log("Point clicked:", data);
      // Navigate to a new page or display a message
      this.$router.push({ name: 'HelloPage' });
    },

    handleQueryMethodChange(newVal) {
      // console.log("Query method changed:", newVal);
      this.queryMethod = newVal;
      this.clearResults();
    },

    handleQueryTypeChange(newVal) {
      // console.log("Query method changed:", newVal);
      this.queryType = newVal;
      this.clearResults();
    },

    handleSubmit() {
      this.loading = true;
      this.aiQuery = '';
      this.bar_chart_data = [];
      this.line_chart_data = [];
      console.log('PN:', this.selectedPN);
      console.log('Area:', this.selectedArea);
      console.log('Category:', this.selectedCategory);
      console.log('Chart Type:', this.selectedChartType);
      console.log(`Date Range: Start: ${this.selectedDate}`, );
      this.selectedStartDate = this.selectedDateRange.startDate;
      this.selectedEndDate = this.selectedDateRange.endDate;
      // Update chart data based on selected chart type
      if (this.selectedChartType === 'Bar') {
        //this.chartData = this.bar_chart_data;
        this.displayChartType = 'Bar';
        this.load_bar_chart()
      } else if (this.selectedChartType === 'Line') {
        // this.chartData = this.line_chart_data;
        this.displayChartType = 'Line';
        this.load_line_chart()
      }
    },
  }
};
</script>
