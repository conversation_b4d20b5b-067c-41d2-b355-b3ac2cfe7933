const configSwitch = require("./config.js");
const config = configSwitch.config();
const ibmdb = require("ibm_db");
const ldap = require("ldapjs");
const logger = require('../utils/logger');
const path = require('path');
const xls = require('xlsjs');


//each exoorts, calls a different function. Pass values from client and the callback
exports.check_db = (req, res) => {

    check_db(req.body, function (err, data) {

        res.status(201).json(data);
    });

};

exports.check_db2 = (req, res) => {

  check_db2(req.body, function (err, data) {

      res.status(201).json(data);
  });

};

exports.populate_chart = (req, res) => {

    populate_chart(req.body, function (err, data) {

        res.status(201).json(data);
    });

};

exports.populate_heatmap = (req, res) => {

  populate_heatmap(req.body, function (err, data) {

      res.status(201).json(data);
  });

};

exports.populate_chart2 = (req, res) => {

  populate_chart2(req.body, function (err, data) {

      res.status(201).json(data);
  });

};

exports.populate_line_chart = (req, res) => {

  populate_line_chart(req.body, function (err, data) {

      res.status(201).json(data);
  });

};

exports.get_fail_count_by_category = (req, res) => {

  getFailCountByCategory(req.body, function (err, data) {

      res.status(201).json(data);
  });

};

exports.get_fail_count_by_category_db = (req, res) => {

  getFailCountByCategoryDB(req.body, function (err, data) {

      res.status(201).json(data);
  });

};

exports.get_fails_drill = (req, res) => {

  getFailsDrill(req.body, function (err, data) {

      res.status(201).json(data);
  });

};

exports.get_unique_pns = (req, res) => {

  getUniquePNs(req.body, function (err, data) {

      res.status(201).json(data);
  });

};

exports.get_unique_categories = (req, res) => {

  getUniqueCategories(req.body, function (err, data) {

      res.status(201).json(data);
  });

};

exports.get_unique_categories_db = (req, res) => {

  getUniqueCategoriesDB(req.body, function (err, data) {

      res.status(201).json(data);
  });

};

exports.get_unique_categories_db2 = (req, res) => {

  getUniqueCategoriesDB2(req.body, function (err, data) {

      res.status(201).json(data);
  });

};

exports.get_unval = (req, res) => {
  // Check if we're querying by Metis group
  if (req.body.metisGroup && !req.body.PN) {
    getUnvalidFailCountbyMetisGroup(req.body, function (err, data) {
      res.status(201).json(data);
    });
  } else {
    getUnvalidFailCountbyPN(req.body, function (err, data) {
      res.status(201).json(data);
    });
  }
};

exports.populate_xf_chart = (req, res) => {

  populate_xf_chart(req.body, function (err, data) {

      res.status(201).json(data);
  });

};

function populate_xf_chart(values, callback) {
  let responseObj = {
      data: [],
      status_res: null
  };

  let filePath = ''
  if (values["process"] === "FAB"){
    filePath = path.join(__dirname, `../excel/FAB 10-24 to 2-25.xls`);
  }else if (values["process"] === "FUL"){
    filePath = path.join(__dirname, `../excel/FUL 10-24 to 2-25.xls`);
  }


  const workbook = xls.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);

  // Initialize an empty array to hold the row objects
  const rowsList = [];
  let group_val = '';
  sheet.forEach(row => {
    // Append each row object to the list
    if (typeof(row.XF) === "number" && row.XF != 0){
      if(row.XF <1){
        group_val = 'Good'
      }else if(row.XF >=1 && row.XF < 1.2){
        group_val = 'Acceptable'
      }else{
        group_val = 'Needs Attention'
      }
      const transformed_row = {
      period: row.Period,
      codeName: row["Code Name"],
      group: group_val,
      xFactor: row.XF,
      xFactor_scaled: Math.min(row.XF,3)
  };
  rowsList.push(transformed_row);
}
  });

  rowsList.sort((a, b) => {
    // Compare by period first
    if (a.period < b.period) return -1;
    if (a.period > b.period) return 1;

    // If periods are equal, compare by xFactor
    return a.xFactor - b.xFactor;
  });
  console.log(rowsList)
  responseObj.data =rowsList;
  responseObj.status_res ="success";
  callback(null, responseObj);
}


function populate_chart(values, callback) {
  let responseObj = {
      data: [],
      status_res: null
  };

  const filePath = path.join(__dirname, `../excel/Drill_data.xls`);
  const workbook = xls.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);

  // Initialize an empty array to hold the row objects
  const rowsList = [];

  sheet.forEach(row => {
    // Append each row object to the list
    rowsList.push(row);
  });
  const hardcodedData = {
      machine_id: '02ED368',

      rows_list: rowsList,
      pn_data: [
      {
        "period": "2024-06",
        "codeName": "Orion",
        "fails": 12,
        "volume": 1461,
        "defRate": 0.82,
        "target": 0.87,
        "xFactor": 0.9440865,
        "group":"XFactor"
      },
      {
        "period": "2024-06",
        "codeName": "Parthenon",
        "fails": 7,
        "volume": 3090,
        "defRate": 0.23,
        "target": 0.40,
        "xFactor": 0.566343,
        "group":"XFactor"
      },
      {
        "period": "2024-06",
        "codeName": "Saiph",
        "fails": 1,
        "volume": 271,
        "defRate": 0.37,
        "target": 0.50,
        "xFactor": 0.7380074,
        "group":"XFactor"
      },
      {
        "period": "2024-06",
        "codeName": "Victoria Crypto",
        "fails": 7,
        "volume": 283,
        "defRate": 2.47,
        "target": 1.90,
        "xFactor": 1.3018412,
        "group":"XFactor"
      }
    ],

    monthly_data: [
      // Data for Passing yields
      { "group": "Passing", "key": "January", "value": 1.00 },  // 65000 scaled to 100
      { "group": "Passing", "key": "April", "value": .78 },     // 51213 scaled to 78
      { "group": "Passing", "key": "June", "value": .66 },      // 45321 scaled to 66
      { "group": "Passing", "key": "July", "value": .83 },      // 57890 scaled to 83
      { "group": "Passing", "key": "August", "value": .88 },    // 62345 scaled to 88
      { "group": "Passing", "key": "September", "value": .64 }, // 49213 scaled to 64
      { "group": "Passing", "key": "October", "value": .81 },   // 58321 scaled to 81
      { "group": "Passing", "key": "November", "value": .92 },  // 67345 scaled to 92
      { "group": "Passing", "key": "December", "value": 1.00 }, // 71234 scaled to 100

      // Data for Acceptable yields
      { "group": "Acceptable", "key": "March", "value": .51 },   // 35213 scaled to 51
      { "group": "Acceptable", "key": "February", "value": .47 }, // 29123 scaled to 43

      // Data for Failing yields
      { "group": "Failing", "key": "May", "value": .20 },        // 16932 scaled to 20

      // Data for Targets (all set to 55)
      { "group": "Targets", "key": "January", "value": .55 },
      { "group": "Targets", "key": "February", "value": .55 },
      { "group": "Targets", "key": "March", "value": .55 },
      { "group": "Targets", "key": "April", "value": .55 },
      { "group": "Targets", "key": "May", "value": .55 },
      { "group": "Targets", "key": "June", "value": .55 },
      { "group": "Targets", "key": "July", "value": .55 },
      { "group": "Targets", "key": "August", "value": .55 },
      { "group": "Targets", "key": "September", "value": .55 },
      { "group": "Targets", "key": "October", "value": .55 },
      { "group": "Targets", "key": "November", "value": .55 },
      { "group": "Targets", "key": "December", "value": .55 },

      // Data for Guardband (set to 45 for each month)
      { "group": "Guardband", "key": "January", "value": .45 },
      { "group": "Guardband", "key": "February", "value": .45 },
      { "group": "Guardband", "key": "March", "value": .45 },
      { "group": "Guardband", "key": "April", "value": .45 },
      { "group": "Guardband", "key": "May", "value": .45 },
      { "group": "Guardband", "key": "June", "value": .45 },
      { "group": "Guardband", "key": "July", "value": .45 },
      { "group": "Guardband", "key": "August", "value": .45 },
      { "group": "Guardband", "key": "September", "value": .45 },
      { "group": "Guardband", "key": "October", "value": .45 },
      { "group": "Guardband", "key": "November", "value": .45 },
      { "group": "Guardband", "key": "December", "value": .45 }
  ]

  };

  responseObj.data =hardcodedData;
  responseObj.status_res ="success";
  callback(null, responseObj);
}

function populate_heatmap(values, callback) {
    let responseObj = {
        data: [],
        status_res: null
    };

    const filePath = path.join(__dirname, `../excel/Drill_data_2.xls`);
    const workbook = xls.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);

    // Initialize an empty array to hold the row objects
    const rowsList = [];

    sheet.forEach(row => {
      row.group = row["Code Name"];
      row.value = row.XFactor
      // if (row.XFactor <= 1){
      // row.value = row.XFactor * 100;
      // }else{
      //   row.value = 100
      // }
      // Append each row object to the list
      rowsList.push(row);
    });


    const hardcodedData = {

        rows_list: rowsList,

    };
    responseObj.data = hardcodedData;
    responseObj.status_res ="success";
    callback(null, responseObj);
}

function populate_line_chart_issue(values, callback) {
  let responseObj = {
      data: [],
      status_res: null
  };

  const filePath = path.join(__dirname, `../excel/Drill_data_2.xls`);
  const workbook = xls.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);

  // Initialize an empty array to hold the row objects
  const rowsList = [];

  sheet.forEach(row => {
    row.group = row["Code Name"];
    row.value = row.XFactor
    // if (row.XFactor <= 1){
    // row.value = row.XFactor * 100;
    // }else{
    //   row.value = 100
    // }
    // Append each row object to the list
    rowsList.push(row);
  });


  const hardcodedData = {

      rows_list: rowsList,

  };
  responseObj.data = hardcodedData;
  responseObj.status_res ="success";
  callback(null, responseObj);
}

function populate_chart2(values, callback) {
  let responseObj = {
      data: [],
      status_res: null
  };


  const hardcodedData = {
      machine_id: '02ED368',
      monthly_data:
      [
          // Data for bars (12-month yields)
          {
            "group": "Yields1",
            "key": "January",
            "value": 65000
          },
          {
            "group": "Yields1",
            "key": "February",
            "value": 29123
          },
          {
            "group": "Yields1",
            "key": "March",
            "value": 35213
          },
          {
            "group": "Yields1",
            "key": "April",
            "value": 51213
          },
          {
            "group": "Yields1",
            "key": "May",
            "value": 16932
          },
          {
            "group": "Yields1",
            "key": "June",
            "value": 45321
          },
          {
            "group": "Yields1",
            "key": "July",
            "value": 57890
          },
          {
            "group": "Yields1",
            "key": "August",
            "value": 62345
          },
          {
            "group": "Yields2",
            "key": "September",
            "value": 49213
          },
          {
            "group": "Yields2",
            "key": "October",
            "value": 58321
          },
          {
            "group": "Yields2",
            "key": "November",
            "value": 67345
          },
          {
            "group": "Yields2",
            "key": "December",
            "value": 71234
          },
          // Data for line (Targets per month)

        ],
  };

  responseObj.data =hardcodedData;
  responseObj.status_res ="success";
  callback(null, responseObj);
}

function populate_line_chart(values, callback) {
  let responseObj = {
      data: [],
      status_res: null
  };


  const hardcodedData = {
      line_data:
      [
        {
          group: 'Dataset 1',
          date: '2023-01-01',
          value: 50000
        },
        {
          group: 'Dataset 1',
          date: '2023-01-05',
          value: 65000
        },
        {
          group: 'Dataset 1',
          date: '2023-01-08',
          value: null
        },
        {
          group: 'Dataset 1',
          date: '2023-01-13',
          value: 49213
        },
        {
          group: 'Dataset 1',
          date: '2023-01-17',
          value: 51213
        },
        {
          group: 'Dataset 2',
          date: '2023-01-02',
          value: 0
        },
        {
          group: 'Dataset 2',
          date: '2023-01-06',
          value: 57312
        },
        {
          group: 'Dataset 2',
          date: '2023-01-08',
          value: 27432
        },
        {
          group: 'Dataset 2',
          date: '2023-01-15',
          value: 70323
        },
        {
          group: 'Dataset 2',
          date: '2023-01-19',
          value: 21300
        },
        {
          group: 'Dataset 3',
          date: '2023-01-01',
          value: 40000
        },
        {
          group: 'Dataset 3',
          date: '2023-01-05',
          value: null
        },
        {
          group: 'Dataset 3',
          date: '2023-01-08',
          value: 18000
        },
        {
          group: 'Dataset 3',
          date: '2023-01-13',
          value: 39213
        },
        {
          group: 'Dataset 3',
          date: '2023-01-17',
          value: 61213
        },
        {
          group: 'Dataset 4',
          date: '2023-01-02',
          value: 20000
        },
        {
          group: 'Dataset 4',
          date: '2023-01-06',
          value: 37312
        },
        {
          group: 'Dataset 4',
          date: '2023-01-08',
          value: 51432
        },
        {
          group: 'Dataset 4',
          date: '2023-01-15',
          value: 25332
        },
        {
          group: 'Dataset 4',
          date: '2023-01-19',
          value: null
        }
        ],
  };

  responseObj.data =hardcodedData;
  responseObj.status_res ="success";
  callback(null, responseObj);
}


async function check_db(values, callback) {

  let responseObj = {
      status_res: null,
      sql_state: null,
      sql_message: null,
      sql_error_msg: null,
      output: null,
      sql_code: null,
      data: []
  };

  let user_type = values["user_type"];
  let action = values["action"];

  // Permissions check simulation (replace with actual function)
  let reply = true;
  let err = false;

  if (reply) {
      console.log("Opening DB2 connection to R0ADB2");

      // Get the DB2 connection configuration for R0ADB2
      const config = configSwitch.config('R0ADB2');
      let connString = "DRIVER={DB2};"
          + "DATABASE=" + 'R0ADB2' + ";"
          + "UID=" + config.database_user + ";"
          + "PWD=" + config.database_password + ";"
          + "HOSTNAME=" + config.database_host + ";"
          + "PORT=" + config.database_port;

      // Open the connection
      ibmdb.open(connString, async function (err, conn) {
          if (err) {
              // Log and handle error
              console.error("Error connecting to DB:", err);
              responseObj.sql_error_msg = err.message;
              return callback(err, responseObj);
          } else {
              console.log("Connected to R0ADB2");

              // Run the query
              conn.query('select a.DEFECT_ID, a.PART_NUM,a.PART_SER, a.INCIDENT_DATE, a.PROCESS, b.ROOT_CAUSE_1, b.ROOT_CAUSE_2 from QODS.GEM_DEFECT as a join QEVAL.GEM_DEFECT_UPDATES as b on a.DEFECT_ID = b.DEFECT_ID where a.PART_NUM = ?', ['0000003HD554'], async function (err, rows) {
                  if (err) {
                      // Log and handle query error
                      console.error("Query Error:", err);
                      responseObj.sql_error_msg = err.message;
                      return callback(err, responseObj);
                  } else {
                      // Success
                      responseObj.status_res = "success";
                      responseObj.output = rows;
                      responseObj.sql_code = 0;

                      console.log("Query result:", rows);

                      // Log success
                      logger.logInfo(`Query executed successfully for user_type: ${user_type} and action: ${action}`, 'admin.controller');
                      return callback(null, responseObj);
                  }
              });

              // Close the connection after the query is completed
              conn.close(function () {
                  console.log("Connection to R0ADB2 closed");
              });
          }
      });

  } else {
      // If permission is denied, return a permission error
      responseObj.status_res = `Permissions DENIED for the following values: ${user_type} ${action}`;
      logger.logError(`Permissions DENIED for user_type: ${user_type} and action: ${action}`, null, 'admin.controller');
      return callback(new Error("Permissions denied"), responseObj);
  }
}

async function check_db2(values, callback) {

  let responseObj = {
      status_res: null,
      sql_state: null,
      sql_message: null,
      sql_error_msg: null,
      output: null,
      sql_code: null,
      data: []
  };

  let user_type = values["user_type"];
  let action = values["action"];

  // Permissions check simulation (replace with actual function)
  let reply = true;
  let err = false;

  if (reply) {
      console.log("Opening DB2 connection to QRYPROD");

      // Get the DB2 connection configuration for R0ADB2
      const config = configSwitch.config('QRYPROD');
      let connString = "DRIVER={DB2};"
          + "DATABASE=" + 'QRYPROD' + ";"
          + "UID=" + config.database_user + ";"
          + "PWD=" + config.database_password + ";"
          + "HOSTNAME=" + config.database_host + ";"
          + "PORT=" + config.database_port;

      // Open the connection
      ibmdb.open(connString, async function (err, conn) {
          if (err) {
              // Log and handle error
              console.error("Error connecting to DB:", err);
              responseObj.sql_error_msg = err.message;
              return callback(err, responseObj);
          } else {
              console.log("Connected to QRYPROD");

              // Run the query
              conn.query('SELECT * FROM MFS2PCOMN.FCSPQD20 WHERE Q2QDPN = ? AND Q2FGID = ? FETCH FIRST 20 ROWS ONLY', ['0000003HD554', 'JEMTRTN'], async function (err, rows) {
                  if (err) {
                      // Log and handle query error
                      console.error("Query Error:", err);
                      responseObj.sql_error_msg = err.message;
                      return callback(err, responseObj);
                  } else {
                      // Success
                      responseObj.status_res = "success";
                      responseObj.output = rows;
                      responseObj.sql_code = 0;

                      console.log("Query result:", rows);

                      // Log success
                      logger.logInfo(`Query executed successfully for user_type: ${user_type} and action: ${action}`, 'admin.controller');
                      return callback(null, responseObj);
                  }
              });

              // Close the connection after the query is completed
              conn.close(function () {
                  console.log("Connection to QRYPROD closed");
              });
          }
      });

  } else {
      // If permission is denied, return a permission error
      responseObj.status_res = `Permissions DENIED for the following values: ${user_type} ${action}`;
      logger.logError(`Permissions DENIED for user_type: ${user_type} and action: ${action}`, null, 'admin.controller');
      return callback(new Error("Permissions denied"), responseObj);
  }
}

function getUniqueCategories(values, callback) {

  let responseObj = {
    status_res: null,
    all_categories: [],
    all_stages: []
  };

  try {
    // Load the Excel file
    const filePath = path.join(__dirname, `../excel/${values["PN"]}_test.xls`);
    const workbook = xls.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);

    // Initialize a Set to store unique categories
    let uniqueCategories = new Set();
    let uniqueStages = new Set();

    // Iterate over the rows in the sheet
    sheet.forEach(row => {
      if (row.KEY_CAT) {
        uniqueCategories.add(row.KEY_CAT);
      }
      if (row.STAGE) {
        uniqueStages.add(row.STAGE);
      }
    });

    // Convert Set to Array for response
    responseObj.all_categories = Array.from(uniqueCategories);
    responseObj.all_stages = Array.from(uniqueStages);


    // Populate the response object
    responseObj.status_res = "success";

    logger.logInfo(`Unique categories: ${JSON.stringify(responseObj.all_categories)}`, 'admin.controller');
    callback(null, responseObj);

  } catch (error) {
    logger.logError(`Error reading Excel file: ${error.message}`, error, 'admin.controller');
    callback(error, responseObj);
  }
}


function getUniquePNs(values, callback) {
  let responseObj = {
    status_res: null,
    all_categories: [],
    all_stages: [],
    sql_error_msg: null
  };

  try {
    // Initialize a Set to store unique categories and stages
    let uniquePNs = new Set();
    let combo_select = [];

    console.log("Opening DB2 connection to R0ADB2");

    // Get the DB2 connection configuration for R0ADB2
    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    // Open the connection
    ibmdb.open(connString, function (err, conn) {
      if (err) {
        // Log and handle error
        console.error("Error connecting to DB:", err);
        responseObj.sql_error_msg = err.message;
        return callback(err, responseObj);
      }

      console.log("Connected to R0ADB2");

      // Run the query
      conn.query(`SELECT DISTINCT PART_NUM FROM QODS.GEM_DEFECT WHERE SYSTEM_TYPE = ?`, ['3931'], function (err, rows) {
        if (err) {
          // Log and handle query error
          console.error("Query Error:", err);
          responseObj.sql_error_msg = err.message;
          conn.close(function() {
            console.log("Connection to R0ADB2 closed");
            return callback(err, responseObj);
          });
        } else {
          // Process rows
          rows.forEach(obj => {
            uniquePNs.add(obj.PART_NUM.trim().slice(5));
          });

          let uniquePN_array = Array.from(uniquePNs);
          uniquePN_array.forEach(obj => {
            combo_select.push({name: obj, label:obj, value:obj})
          })

          responseObj.all_pns = combo_select;

          responseObj.status_res = "success";
          responseObj.sql_code = 0;

          console.log("Query result:", rows);

          // Close the connection and call the callback
          conn.close(function() {
            console.log("Connection to R0ADB2 closed");
            return callback(null, responseObj);
          });
        }
      });
    });

  } catch (error) {
    console.error(`Error in getUniqueCategoriesDB: ${error.message}`);
    responseObj.sql_error_msg = error.message;
    callback(error, responseObj);
  }
}

function getUniqueCategoriesDB(values, callback) {
  let responseObj = {
    status_res: null,
    all_categories: [],
    all_stages: [],
    sql_error_msg: null
  };

  try {
    // Initialize a Set to store unique categories and stages
    let uniqueCategories = new Set();
    let uniqueStages = new Set();

    console.log("Opening DB2 connection to R0ADB2");

    // Get the DB2 connection configuration for R0ADB2
    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    // Open the connection
    ibmdb.open(connString, function (err, conn) {
      if (err) {
        // Log and handle error
        console.error("Error connecting to DB:", err);
        responseObj.sql_error_msg = err.message;
        return callback(err, responseObj);
      }

      console.log("Connected to R0ADB2");

      // Run the query
      conn.query('SELECT DISTINCT a.PROCESS, b.ROOT_CAUSE_1 FROM QODS.GEM_DEFECT AS a JOIN QEVAL.GEM_DEFECT_UPDATES AS b ON a.DEFECT_ID = b.DEFECT_ID WHERE a.PART_NUM = ?', ['00000'+ values["PN"]], function (err, rows) {
        if (err) {
          // Log and handle query error
          console.error("Query Error:", err);
          responseObj.sql_error_msg = err.message;
          conn.close(function() {
            console.log("Connection to R0ADB2 closed");
            return callback(err, responseObj);
          });
        } else {
          // Process rows
          rows.forEach(obj => {
            // checks if process and root cause arent null
            if(obj.PROCESS && obj.ROOT_CAUSE_1){
            // Trim and add the values to sets
            uniqueStages.add(obj.PROCESS.trim());
            uniqueCategories.add(obj.ROOT_CAUSE_1.trim());
            }

          });

          responseObj.all_categories = Array.from(uniqueCategories);
          responseObj.all_stages = Array.from(uniqueStages);

          responseObj.status_res = "success";
          responseObj.sql_code = 0;

          console.log("Query result:", rows);

          // Close the connection and call the callback
          conn.close(function() {
            console.log("Connection to R0ADB2 closed");
            return callback(null, responseObj);
          });
        }
      });
    });

  } catch (error) {
    console.error(`Error in getUniqueCategoriesDB: ${error.message}`);
    responseObj.sql_error_msg = error.message;
    callback(error, responseObj);
  }
}

function getUniqueCategoriesDB2(values, callback) {
  let responseObj = {
    status_res: null,
    all_categories: [],
    all_stages: [],
    sql_error_msg: null
  };

  try {
    // Initialize a Set to store unique categories and stages
    let uniqueCategories = new Set();
    let uniqueStages = new Set();

    console.log("Opening DB2 connection to R0ADB2");

    // Get the DB2 connection configuration for R0ADB2
    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    // Open the connection
    ibmdb.open(connString, function (err, conn) {
      if (err) {
        // Log and handle error
        console.error("Error connecting to DB:", err);
        responseObj.sql_error_msg = err.message;
        return callback(err, responseObj);
      }

      console.log("Connected to R0ADB2");

      let qrystr = ''
      let qryvals = []
      // query string
      if(values["area"]){
         qrystr = 'SELECT DISTINCT a.PROCESS, b.ROOT_CAUSE_1 FROM QODS.GEM_DEFECT AS a JOIN QEVAL.GEM_DEFECT_UPDATES AS b ON a.DEFECT_ID = b.DEFECT_ID WHERE a.PART_NUM = ? and a.PROCESS = ?'
         qryvals = ['00000'+ values["PN"], values["area"]]
        } else if(values["category"]){
        qrystr = 'SELECT DISTINCT a.PROCESS, b.ROOT_CAUSE_1 FROM QODS.GEM_DEFECT AS a JOIN QEVAL.GEM_DEFECT_UPDATES AS b ON a.DEFECT_ID = b.DEFECT_ID WHERE a.PART_NUM = ? and b.ROOT_CAUSE_1 = ?'
        qryvals = ['00000'+ values["PN"], values["category"]]
      }

      // Run the query
      conn.query(qrystr, qryvals, function (err, rows) {
        if (err) {
          // Log and handle query error
          console.error("Query Error:", err);
          responseObj.sql_error_msg = err.message;
          conn.close(function() {
            console.log("Connection to R0ADB2 closed");
            return callback(err, responseObj);
          });
        } else {
          // Process rows
          rows.forEach(obj => {
            // checks if process and root cause arent null
            if(obj.PROCESS && obj.ROOT_CAUSE_1){
            // Trim and add the values to sets
            uniqueStages.add(obj.PROCESS.trim());
            uniqueCategories.add(obj.ROOT_CAUSE_1.trim());
            }

          });
          if(values["area"]){
            responseObj.new_array = Array.from(uniqueCategories);
          }else if (values["category"])
            responseObj.new_array = Array.from(uniqueStages);

          responseObj.status_res = "success";
          responseObj.sql_code = 0;

          console.log("Query result:", rows);

          // Close the connection and call the callback
          conn.close(function() {
            console.log("Connection to R0ADB2 closed");
            return callback(null, responseObj);
          });
        }
      });
    });

  } catch (error) {
    console.error(`Error in getUniqueCategoriesDB: ${error.message}`);
    responseObj.sql_error_msg = error.message;
    callback(error, responseObj);
  }
}


function getFailCountByCategory(values, callback) {
  const category = values["category"];
  const area = values["area"];
  const PN = values["PN"];

  const filePath = path.join(__dirname, `../excel/${PN}_test.xls`);

  let responseObj = {
    status_res: null,
    counts_by_period: {}
  };

  try {
    // Load the Excel file
    const workbook = xls.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);

    // Initialize a count object to store results by period
    let countsByPeriod = {};

    // Iterate over the rows in the sheet

    if (!area){
    sheet.forEach(row => {
      const rowCategory = row.KEY_CAT;
      const period = row.Period;

      // Check if the row matches the desired category (or include all if category is 'All')

      if (category === 'All' || rowCategory === category) {
        if (!countsByPeriod[period]) {
          countsByPeriod[period] = 0;
        }
        countsByPeriod[period]++;
      }

    });
  }else{
    sheet.forEach(row => {
      const rowCategory = row.KEY_CAT;
      const rowArea = row.STAGE;
      const period = row.Period;

      // Check if the row matches the desired category (or include all if category is 'All')

      if (rowArea === area && rowCategory === category) {
        if (!countsByPeriod[period]) {
          countsByPeriod[period] = 0;
        }
        countsByPeriod[period]++;
      }

    });

  }

    const arrayOfObjects = Object.keys(countsByPeriod).map(key => ({
      key: key,    // The key from the original object
      value: countsByPeriod[key],  // The value from the original object
      group: "Defect Count"
    }));

    // Populate the response object
    responseObj.status_res = "success";
    responseObj.counts_by_period = arrayOfObjects;

    logger.logInfo(`Count of fails for category '${category}' separated by period: ${JSON.stringify(countsByPeriod)}`, 'admin.controller');
    callback(null, responseObj);

  } catch (error) {
    logger.logError(`Error reading Excel file: ${error.message}`, error, 'admin.controller');
    callback(error, responseObj);
  }
}


function getFailCountByCategoryDB(values, callback) {


  let responseObj = {
    status_res: null,
    counts_by_period: []
  };
  console.log("Opening DB2 connection to R0ADB2");

  // Get the DB2 connection configuration for R0ADB2
  const config = configSwitch.config('R0ADB2');
  let connString = "DRIVER={DB2};"
    + "DATABASE=" + 'R0ADB2' + ";"
    + "UID=" + config.database_user + ";"
    + "PWD=" + config.database_password + ";"
    + "HOSTNAME=" + config.database_host + ";"
    + "PORT=" + config.database_port;

  // Open the connection
  ibmdb.open(connString, function (err, conn) {
    if (err) {
      // Log and handle error
      console.error("Error connecting to DB:", err);
      responseObj.sql_error_msg = err.message;
      return callback(err, responseObj);
    }

    console.log("Connected to R0ADB2");
    console.log("Test values", values)

    // Run the query
    conn.query(`SELECT TO_CHAR(a.INCIDENT_DATE, 'YYYY-MM') AS year_month, a.PART_NUM, a.PROCESS, b.ROOT_CAUSE_1 AS root_cause, COUNT(*) AS defect_count FROM QODS.GEM_DEFECT AS a JOIN QEVAL.GEM_DEFECT_UPDATES AS b ON a.DEFECT_ID = b.DEFECT_ID WHERE a.PART_NUM = ? AND a.PROCESS = ? AND a.INCIDENT_DATE > ? AND a.INCIDENT_DATE < ? AND b.ROOT_CAUSE_1 = ? GROUP BY TO_CHAR(a.INCIDENT_DATE, 'YYYY-MM'), a.PART_NUM, a.PROCESS, b.ROOT_CAUSE_1 ORDER BY year_month, a.PART_NUM, a.PROCESS, root_cause;
` , ['00000'+ values["PN"], values["area"], values["startDate"], values["endDate"], values["category"]], function (err, rows) {
      if (err) {
        // Log and handle query error
        console.error("Query Error:", err);
        responseObj.sql_error_msg = err.message;
        conn.close(function() {
          console.log("Connection to R0ADB2 closed");
          return callback(err, responseObj);
        });
      } else {
        // Process rows - return both simplified format for charts and full data
        const counts = []
        const fullData = []
        rows.forEach(obj => {
          // Simplified format for existing chart compatibility
          counts.push({key: obj.YEAR_MONTH, value: obj.DEFECT_COUNT, group: "Defect Count"})

          // Full data format for Phase1B
          fullData.push({
            YEAR_MONTH: obj.YEAR_MONTH,
            PART_NUM: obj.PART_NUM,
            PROCESS: obj.PROCESS,
            ROOT_CAUSE: obj.ROOT_CAUSE,
            DEFECT_COUNT: obj.DEFECT_COUNT
          })
        });

        responseObj.counts_by_period = counts;
        responseObj.full_data = fullData;
        responseObj.status_res = "success";
        responseObj.sql_code = 0;

        console.log("Query result:", rows);

        // Close the connection and call the callback
        conn.close(function() {
          console.log("Connection to R0ADB2 closed");
          return callback(null, responseObj);
        });
      }
    });
  });

}

function getFailsDrill(values, callback) {
  const data = values["data"];  // Extract the data array from values
  const columnName = values["column"];
  const newColumnName = values["new_column"];
  const selectedVar = values["var"];
  console.log("selected", selectedVar)
  console.log("col", columnName)
  let responseObj = {
    status_res: null,
    counts_by_name: [],
    filtered_data: []
  };

  try {
    // Initialize count and filtered array
    let overallCount = 0;
    const filteredData = [];
    const breakoutCounts = {};

    // Iterate over each row in the data array
    data.forEach(row => {
      if (row[columnName] === selectedVar) {
        overallCount += 1;
        filteredData.push(row);


        // Break out counts by values in newColumnName
        if (newColumnName && row[newColumnName] !== undefined) {

          const key = row[newColumnName];
          if (!breakoutCounts[key]) {
            breakoutCounts[key] = 0;
          }
          breakoutCounts[key] += 1;
        }
      }
    });

    // Populate the response object
    responseObj.status_res = "success";



    // Add breakout counts if newColumnName was used
    if (newColumnName) {
      Object.keys(breakoutCounts).forEach(key => {
        responseObj.counts_by_name.push({
          key: key,
          value: breakoutCounts[key],
          group: `${newColumnName}`
        });
      });
    }else{
      responseObj.counts_by_name.push({
        key: selectedVar,
        value: overallCount,
        group: "Defect Count"
      });
    }

    responseObj.filtered_data = filteredData;

    callback(null, responseObj);

  } catch (error) {
    callback(error, responseObj);
  }
}

// Function to get validation data for a Metis group
function getUnvalidFailCountbyMetisGroup(values, callback) {
  let responseObj = {
    status_res: null,
  };

  // Check if we have a Metis group
  if (!values["metisGroup"]) {
    console.error("No Metis group provided");
    responseObj.status_res = "error";
    responseObj.error_message = "No Metis group provided";
    return callback(new Error("No Metis group provided"), responseObj);
  }

  console.log(`Getting validation data for Metis group: ${values["metisGroup"]}`);

  // First, we need to get the part numbers for this Metis group from the Excel file
  try {
    const xlsx = require('xlsx');
    const fs = require('fs');
    const path = require('path');

    // Load the Excel file
    const filePath = path.join(__dirname, '../excel/new_metis_test.xlsx');

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error(`Excel file not found at path: ${filePath}`);
      responseObj.status_res = "error";
      responseObj.error_message = "Metis Excel file not found";
      return callback(new Error("Metis Excel file not found"), responseObj);
    }

    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0]; // Assuming data is in the first sheet
    const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

    // Check if the required columns exist
    const hasFullBreakoutName = sheet.length > 0 && 'Full Breakout Name' in sheet[0];
    const hasPNColumn = sheet.length > 0 && 'PN' in sheet[0];

    if (!hasFullBreakoutName || !hasPNColumn) {
      // Log all available columns to help diagnose the issue
      if (sheet.length > 0) {
        console.error('Available columns in the Excel file:', Object.keys(sheet[0]));
      }
      responseObj.status_res = "error";
      responseObj.error_message = "Required columns not found in Metis Excel file";
      return callback(new Error("Required columns not found in Metis Excel file"), responseObj);
    }

    // Filter rows by the specified breakout name
    const filteredRows = sheet.filter(row => row['Full Breakout Name'] === values["metisGroup"]);
    console.log(`Found ${filteredRows.length} rows for Metis group: ${values["metisGroup"]}`);

    if (filteredRows.length === 0) {
      responseObj.status_res = "error";
      responseObj.error_message = `No part numbers found for Metis group: ${values["metisGroup"]}`;
      return callback(new Error(`No part numbers found for Metis group: ${values["metisGroup"]}`), responseObj);
    }

    // Extract part numbers from the filtered rows
    const partNumbers = [...new Set(filteredRows.map(item => item.PN).filter(pn => pn))];
    console.log(`Found ${partNumbers.length} unique part numbers for Metis group: ${values["metisGroup"]}`);

    if (partNumbers.length === 0) {
      responseObj.status_res = "error";
      responseObj.error_message = `No valid part numbers found for Metis group: ${values["metisGroup"]}`;
      return callback(new Error(`No valid part numbers found for Metis group: ${values["metisGroup"]}`), responseObj);
    }

    // Now we have the part numbers, we need to query the validation data for each one
    let totalUnCount = 0;
    let totalValCount = 0;
    const allUnFails = [];
    const allValFails = [];

    // Get the DB2 connection configuration for R0ADB2
    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    // Open the connection
    ibmdb.open(connString, async function (err, conn) {
      if (err) {
        // Log and handle error
        console.error("Error connecting to DB:", err);
        responseObj.sql_error_msg = err.message;
        return callback(err, responseObj);
      }

      console.log("Connected to R0ADB2");

      // Build the query based on whether we have date parameters
      let qrystr = '';
      let baseParams = [];

      if (values["SD"]) {
        // Query with date range
        qrystr = `SELECT
          a.DEFECT_ID,
          a.PART_NUM,
          a.PART_SER,
          a.INCIDENT_DATE,
          TO_CHAR(a.INCIDENT_DATE, 'YYYY-MM') AS year_month,
          a.PROCESS,
          'unvalidated' AS STATUS,
          CAST('' AS VARCHAR(100)) AS ROOT_CAUSE_1,
          CAST('' AS VARCHAR(100)) AS ROOT_CAUSE_2
        FROM QODS.GEM_DEFECT AS a
        LEFT JOIN QEVAL.GEM_DEFECT_UPDATES AS b
        ON a.DEFECT_ID = b.DEFECT_ID
        WHERE b.DEFECT_ID IS NULL
          AND a.PART_NUM IN (${partNumbers.map(() => '?').join(', ')})
          AND a.INCIDENT_DATE > ?
          AND a.INCIDENT_DATE < ?
        UNION ALL
        SELECT
          a.DEFECT_ID,
          a.PART_NUM,
          a.PART_SER,
          a.INCIDENT_DATE,
          TO_CHAR(a.INCIDENT_DATE, 'YYYY-MM') AS year_month,
          a.PROCESS,
          'validated' AS STATUS,
          COALESCE(b.ROOT_CAUSE_1, CAST('' AS VARCHAR(100))) AS ROOT_CAUSE_1,
          COALESCE(b.ROOT_CAUSE_2, CAST('' AS VARCHAR(100))) AS ROOT_CAUSE_2
        FROM QODS.GEM_DEFECT AS a
        JOIN QEVAL.GEM_DEFECT_UPDATES AS b
        ON a.DEFECT_ID = b.DEFECT_ID
        WHERE a.PART_NUM IN (${partNumbers.map(() => '?').join(', ')})
          AND a.INCIDENT_DATE > ?
          AND a.INCIDENT_DATE < ?`;

        baseParams = [
          ...partNumbers.map(pn => pn.length >= 5 ? pn : '00000' + pn),
          values["SD"],
          values["ED"],
          ...partNumbers.map(pn => pn.length >= 5 ? pn : '00000' + pn),
          values["SD"],
          values["ED"]
        ];
      } else {
        // Query without date range
        qrystr = `SELECT
          a.DEFECT_ID,
          a.PART_NUM,
          a.PART_SER,
          a.INCIDENT_DATE,
          TO_CHAR(a.INCIDENT_DATE, 'YYYY-MM') AS year_month,
          a.PROCESS,
          'unvalidated' AS STATUS,
          CAST('' AS VARCHAR(100)) AS ROOT_CAUSE_1,
          CAST('' AS VARCHAR(100)) AS ROOT_CAUSE_2
        FROM QODS.GEM_DEFECT AS a
        LEFT JOIN QEVAL.GEM_DEFECT_UPDATES AS b
        ON a.DEFECT_ID = b.DEFECT_ID
        WHERE b.DEFECT_ID IS NULL
          AND a.PART_NUM IN (${partNumbers.map(() => '?').join(', ')})
        UNION ALL
        SELECT
          a.DEFECT_ID,
          a.PART_NUM,
          a.PART_SER,
          a.INCIDENT_DATE,
          TO_CHAR(a.INCIDENT_DATE, 'YYYY-MM') AS year_month,
          a.PROCESS,
          'validated' AS STATUS,
          COALESCE(b.ROOT_CAUSE_1, CAST('' AS VARCHAR(100))) AS ROOT_CAUSE_1,
          COALESCE(b.ROOT_CAUSE_2, CAST('' AS VARCHAR(100))) AS ROOT_CAUSE_2
        FROM QODS.GEM_DEFECT AS a
        JOIN QEVAL.GEM_DEFECT_UPDATES AS b
        ON a.DEFECT_ID = b.DEFECT_ID
        WHERE a.PART_NUM IN (${partNumbers.map(() => '?').join(', ')})`;

        baseParams = [
          ...partNumbers.map(pn => pn.length >= 5 ? pn : '00000' + pn),
          ...partNumbers.map(pn => pn.length >= 5 ? pn : '00000' + pn)
        ];
      }

      // Add process filter if specified
      if (values["process"]) {
        qrystr = qrystr.replace(/WHERE b\.DEFECT_ID IS NULL/, `WHERE b.DEFECT_ID IS NULL AND a.PROCESS = '${values["process"]}'`);
        qrystr = qrystr.replace(/WHERE a\.PART_NUM IN/, `WHERE a.PROCESS = '${values["process"]}' AND a.PART_NUM IN`);
      }

      // Execute the query
      conn.query(qrystr, baseParams, function (err, rows) {
        if (err) {
          // Log and handle query error
          console.error("Query Error:", err);
          responseObj.sql_error_msg = err.message;
          conn.close(function () {
            console.log("Connection to R0ADB2 closed");
            return callback(err, responseObj);
          });
        } else {
          // Process rows to separate validated and unvalidated failures
          rows.forEach(obj => {
            if (obj.STATUS === 'unvalidated') {
              totalUnCount++;
              allUnFails.push({
                defect_id: obj.DEFECT_ID,
                pn: obj.PART_NUM,
                sn: obj.PART_SER,
                date: obj.INCIDENT_DATE,
                process: obj.PROCESS
              });
            } else if (obj.STATUS === 'validated') {
              totalValCount++;
              allValFails.push({
                defect_id: obj.DEFECT_ID,
                pn: obj.PART_NUM,
                sn: obj.PART_SER,
                date: obj.INCIDENT_DATE,
                process: obj.PROCESS,
                root_cause_1: obj.ROOT_CAUSE_1,
                root_cause_2: obj.ROOT_CAUSE_2
              });
            }
          });

          // Build the response object
          responseObj.unvalidated_fails = allUnFails;
          responseObj.validated_fails = allValFails;
          responseObj.unvalidated_count = totalUnCount;
          responseObj.validated_count = totalValCount;
          responseObj.total_fails = totalUnCount + totalValCount;
          responseObj.perc_val = totalUnCount + totalValCount > 0 ? (totalValCount / (totalUnCount + totalValCount) * 100) : 0;
          responseObj.status_res = "success";
          responseObj.sql_code = 0;
          responseObj.metisGroup = values["metisGroup"];
          responseObj.partNumbers = partNumbers;

          console.log(`Query result for Metis group ${values["metisGroup"]}: ${totalUnCount} unvalidated, ${totalValCount} validated`);

          // Close the connection and call the callback
          conn.close(function () {
            console.log("Connection to R0ADB2 closed");
            return callback(null, responseObj);
          });
        }
      });
    });
  } catch (error) {
    console.error(`Error in getUnvalidFailCountbyMetisGroup: ${error.message}`);
    responseObj.status_res = "error";
    responseObj.error_message = error.message;
    return callback(error, responseObj);
  }
}

function getUnvalidFailCountbyPN(values, callback) {
  let responseObj = {
    status_res: null,
  };

  let unCount = 0;
  let valCount = 0;
  const unFails = [];
  const valFails = [];
  let qrystr = ``;
  let qrylst = [];

  // Check if we have a part number
  if (!values["PN"]) {
    console.error("No part number provided");
    responseObj.status_res = "error";
    responseObj.error_message = "No part number provided";
    return callback(new Error("No part number provided"), responseObj);
  }

  console.log("Opening DB2 connection to R0ADB2");

  // Get the DB2 connection configuration for R0ADB2
  const config = configSwitch.config('R0ADB2');
  let connString = "DRIVER={DB2};"
    + "DATABASE=" + 'R0ADB2' + ";"
    + "UID=" + config.database_user + ";"
    + "PWD=" + config.database_password + ";"
    + "HOSTNAME=" + config.database_host + ";"
    + "PORT=" + config.database_port;

  // Open the connection
  ibmdb.open(connString, function (err, conn) {
    if (err) {
      // Log and handle error
      console.error("Error connecting to DB:", err);
      responseObj.sql_error_msg = err.message;
      return callback(err, responseObj);
    }

    console.log("Connected to R0ADB2");

    // Format the part number with leading zeros if needed
    const formattedPN = values["PN"].length >= 5 ? values["PN"] : '00000' + values["PN"];

    // Build the query based on whether we have date parameters
    if (values["SD"]) {
      // Query with date range
      qrystr = `SELECT
        a.DEFECT_ID,
        a.PART_NUM,
        a.PART_SER,
        a.INCIDENT_DATE,
        TO_CHAR(a.INCIDENT_DATE, 'YYYY-MM') AS year_month,
        a.PROCESS,
        'unvalidated' AS STATUS,
        CAST('' AS VARCHAR(100)) AS ROOT_CAUSE_1,
        CAST('' AS VARCHAR(100)) AS ROOT_CAUSE_2
      FROM QODS.GEM_DEFECT AS a
      LEFT JOIN QEVAL.GEM_DEFECT_UPDATES AS b
      ON a.DEFECT_ID = b.DEFECT_ID
      WHERE b.DEFECT_ID IS NULL
        AND a.PART_NUM = ?
        AND a.INCIDENT_DATE > ?
        AND a.INCIDENT_DATE < ?
      UNION ALL
      SELECT
        a.DEFECT_ID,
        a.PART_NUM,
        a.PART_SER,
        a.INCIDENT_DATE,
        TO_CHAR(a.INCIDENT_DATE, 'YYYY-MM') AS year_month,
        a.PROCESS,
        'validated' AS STATUS,
        COALESCE(b.ROOT_CAUSE_1, CAST('' AS VARCHAR(100))) AS ROOT_CAUSE_1,
        COALESCE(b.ROOT_CAUSE_2, CAST('' AS VARCHAR(100))) AS ROOT_CAUSE_2
      FROM QODS.GEM_DEFECT AS a
      JOIN QEVAL.GEM_DEFECT_UPDATES AS b
      ON a.DEFECT_ID = b.DEFECT_ID
      WHERE a.PART_NUM = ?
        AND a.INCIDENT_DATE > ?
        AND a.INCIDENT_DATE < ?`;

      qrylst = [formattedPN, values["SD"], values["ED"], formattedPN, values["SD"], values["ED"]];
      console.log("Query parameters:", qrylst);
    } else {
      // Query without date range
      qrystr = `SELECT
        a.DEFECT_ID,
        a.PART_NUM,
        a.PART_SER,
        a.INCIDENT_DATE,
        TO_CHAR(a.INCIDENT_DATE, 'YYYY-MM') AS year_month,
        a.PROCESS,
        'unvalidated' AS STATUS,
        CAST('' AS VARCHAR(100)) AS ROOT_CAUSE_1,
        CAST('' AS VARCHAR(100)) AS ROOT_CAUSE_2
      FROM QODS.GEM_DEFECT AS a
      LEFT JOIN QEVAL.GEM_DEFECT_UPDATES AS b
      ON a.DEFECT_ID = b.DEFECT_ID
      WHERE b.DEFECT_ID IS NULL
        AND a.PART_NUM = ?
      UNION ALL
      SELECT
        a.DEFECT_ID,
        a.PART_NUM,
        a.PART_SER,
        a.INCIDENT_DATE,
        TO_CHAR(a.INCIDENT_DATE, 'YYYY-MM') AS year_month,
        a.PROCESS,
        'validated' AS STATUS,
        COALESCE(b.ROOT_CAUSE_1, CAST('' AS VARCHAR(100))) AS ROOT_CAUSE_1,
        COALESCE(b.ROOT_CAUSE_2, CAST('' AS VARCHAR(100))) AS ROOT_CAUSE_2
      FROM QODS.GEM_DEFECT AS a
      JOIN QEVAL.GEM_DEFECT_UPDATES AS b
      ON a.DEFECT_ID = b.DEFECT_ID
      WHERE a.PART_NUM = ?`;

      qrylst = [formattedPN, formattedPN];
    }

    // Add process filter if specified
    if (values["process"]) {
      qrystr = qrystr.replace(/WHERE b\.DEFECT_ID IS NULL/, `WHERE b.DEFECT_ID IS NULL AND a.PROCESS = '${values["process"]}'`);
      qrystr = qrystr.replace(/WHERE a\.PART_NUM = \?( AND a\.INCIDENT_DATE)/, `WHERE a.PART_NUM = ? AND a.PROCESS = '${values["process"]}'$1`);
    }

    // Execute the query
    conn.query(qrystr, qrylst, function (err, rows) {
      if (err) {
        // Log and handle query error
        console.error("Query Error:", err);
        responseObj.sql_error_msg = err.message;
        conn.close(function () {
          console.log("Connection to R0ADB2 closed");
          return callback(err, responseObj);
        });
      } else {
        // Process rows to separate validated and unvalidated failures
        rows.forEach(obj => {
          if (obj.STATUS === 'unvalidated') {
            unCount++;
            unFails.push({
              defect_id: obj.DEFECT_ID,
              pn: obj.PART_NUM,
              sn: obj.PART_SER,
              date: obj.INCIDENT_DATE,
              process: obj.PROCESS
            });
          } else if (obj.STATUS === 'validated') {
            valCount++;
            valFails.push({
              defect_id: obj.DEFECT_ID,
              pn: obj.PART_NUM,
              sn: obj.PART_SER,
              date: obj.INCIDENT_DATE,
              process: obj.PROCESS,
              root_cause_1: obj.ROOT_CAUSE_1,
              root_cause_2: obj.ROOT_CAUSE_2
            });
          }
        });

        // Build the response object
        responseObj.unvalidated_fails = unFails;
        responseObj.validated_fails = valFails;
        responseObj.unvalidated_count = unCount;
        responseObj.validated_count = valCount;
        responseObj.total_fails = unCount + valCount;
        responseObj.perc_val = unCount + valCount > 0 ? (valCount / (unCount + valCount) * 100) : 0;
        responseObj.status_res = "success";
        responseObj.sql_code = 0;

        console.log(`Query result: ${unCount} unvalidated, ${valCount} validated`);

        // Close the connection and call the callback
        conn.close(function () {
          console.log("Connection to R0ADB2 closed");
          return callback(null, responseObj);
        });
      }
    });
  });
}