const configSwitch = require("./config.js");
const ibmdb = require("ibm_db");
const path = require('path');
const xlsx = require('xlsx');
const fs = require('fs');
const logger = require('../utils/logger');

// Export controller functions
exports.get_fail_count_by_category_db = (req, res) => {
  getFailCountByCategoryDb(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_fail_count_by_category_db:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_unique_categories_db = (req, res) => {
  getUniqueCategoriesDb(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_unique_categories_db:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_unique_categories_db2 = (req, res) => {
  getUniqueCategoriesDb2(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_unique_categories_db2:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_unique_pns = (req, res) => {
  getUniquePns(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_unique_pns:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_combo_defrate_data = (req, res) => {
  getComboDefrateData(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_combo_defrate_data:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_suppliers = (req, res) => {
  getSuppliers(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_suppliers:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_vintages = (req, res) => {
  getVintages(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_vintages:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_compare_data = (req, res) => {
  getCompareData(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_compare_data:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

// Helper function to get database connection
function getDbConnection() {
  const config = configSwitch.config();
  return config.db_connection_string;
}

// Get fail count by category from database
async function getFailCountByCategoryDb(values, callback) {
  let responseObj = {
    status_res: "success",
    counts_by_period: [],
    full_data: []
  };

  try {
    const { PN, category, area, startDate, endDate } = values;
    
    logger.logInfo(`Getting fail count for PN: ${PN}, Category: ${category}, Area: ${area}`, 'getFailCountByCategoryDb');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    let sql = `
      SELECT 
        YEAR(a.INCIDENT_DATE) as YEAR,
        MONTH(a.INCIDENT_DATE) as MONTH,
        CONCAT(YEAR(a.INCIDENT_DATE), '-', LPAD(MONTH(a.INCIDENT_DATE), 2, '0')) as YEAR_MONTH,
        COUNT(*) as DEFECT_COUNT
      FROM QODS.GEM_DEFECT a
      LEFT JOIN QEVAL.GEM_DEFECT_UPDATES b ON a.DEFECT_ID = b.DEFECT_ID
      WHERE 1=1
    `;

    let queryParams = [];

    if (PN) {
      sql += ` AND a.PART_NUM = ?`;
      queryParams.push(PN);
    }

    if (category) {
      sql += ` AND TRIM(b.ROOT_CAUSE_1) = ?`;
      queryParams.push(category);
    }

    if (area) {
      sql += ` AND a.PROCESS = ?`;
      queryParams.push(area);
    }

    if (startDate && endDate) {
      sql += ` AND a.INCIDENT_DATE BETWEEN ? AND ?`;
      queryParams.push(startDate, endDate);
    }

    sql += ` GROUP BY YEAR(a.INCIDENT_DATE), MONTH(a.INCIDENT_DATE)
             ORDER BY YEAR(a.INCIDENT_DATE), MONTH(a.INCIDENT_DATE)`;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, queryParams, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        responseObj.full_data = rows;
        responseObj.counts_by_period = rows.map(row => ({
          key: row.YEAR_MONTH,
          value: row.DEFECT_COUNT,
          group: "Defect Count"
        }));

        logger.logInfo(`Retrieved ${rows.length} data points`, 'getFailCountByCategoryDb');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getFailCountByCategoryDb:', error);
    callback(error, null);
  }
}

// Get unique categories from database
async function getUniqueCategoriesDb(values, callback) {
  let responseObj = {
    status_res: "success",
    all_categories: [],
    all_stages: []
  };

  try {
    const { PN } = values;
    
    logger.logInfo(`Getting unique categories for PN: ${PN}`, 'getUniqueCategoriesDb');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    // Get categories
    let categorySql = `
      SELECT DISTINCT TRIM(b.ROOT_CAUSE_1) as CATEGORY
      FROM QODS.GEM_DEFECT a
      LEFT JOIN QEVAL.GEM_DEFECT_UPDATES b ON a.DEFECT_ID = b.DEFECT_ID
      WHERE b.ROOT_CAUSE_1 IS NOT NULL AND TRIM(b.ROOT_CAUSE_1) != ''
    `;

    // Get stages
    let stageSql = `
      SELECT DISTINCT a.PROCESS as STAGE
      FROM QODS.GEM_DEFECT a
      WHERE a.PROCESS IS NOT NULL AND TRIM(a.PROCESS) != ''
    `;

    if (PN) {
      categorySql += ` AND a.PART_NUM = '${PN}'`;
      stageSql += ` AND a.PART_NUM = '${PN}'`;
    }

    categorySql += ` ORDER BY CATEGORY`;
    stageSql += ` ORDER BY STAGE`;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      // Get categories
      conn.query(categorySql, function(err, categoryRows) {
        if (err) {
          logger.logError('Database query error for categories:', err);
          conn.close();
          callback(err, null);
          return;
        }

        responseObj.all_categories = categoryRows.map(row => row.CATEGORY);

        // Get stages
        conn.query(stageSql, function(err, stageRows) {
          if (err) {
            logger.logError('Database query error for stages:', err);
            conn.close();
            callback(err, null);
            return;
          }

          responseObj.all_stages = stageRows.map(row => row.STAGE);

          logger.logInfo(`Retrieved ${responseObj.all_categories.length} categories and ${responseObj.all_stages.length} stages`, 'getUniqueCategoriesDb');
          conn.close();
          callback(null, responseObj);
        });
      });
    });

  } catch (error) {
    logger.logError('Error in getUniqueCategoriesDb:', error);
    callback(error, null);
  }
}

// Get unique categories with additional filtering
async function getUniqueCategoriesDb2(values, callback) {
  let responseObj = {
    status_res: "success",
    new_array: []
  };

  try {
    const { PN, area, category } = values;
    
    logger.logInfo(`Getting filtered categories for PN: ${PN}, Area: ${area}, Category: ${category}`, 'getUniqueCategoriesDb2');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    let sql = '';
    
    if (area) {
      // Get categories for the specified area
      sql = `
        SELECT DISTINCT TRIM(b.ROOT_CAUSE_1) as ITEM
        FROM QODS.GEM_DEFECT a
        LEFT JOIN QEVAL.GEM_DEFECT_UPDATES b ON a.DEFECT_ID = b.DEFECT_ID
        WHERE b.ROOT_CAUSE_1 IS NOT NULL AND TRIM(b.ROOT_CAUSE_1) != ''
        AND a.PROCESS = '${area}'
      `;
    } else if (category) {
      // Get areas for the specified category
      sql = `
        SELECT DISTINCT a.PROCESS as ITEM
        FROM QODS.GEM_DEFECT a
        LEFT JOIN QEVAL.GEM_DEFECT_UPDATES b ON a.DEFECT_ID = b.DEFECT_ID
        WHERE a.PROCESS IS NOT NULL AND TRIM(a.PROCESS) != ''
        AND TRIM(b.ROOT_CAUSE_1) = '${category}'
      `;
    }

    if (PN) {
      sql += ` AND a.PART_NUM = '${PN}'`;
    }

    sql += ` ORDER BY ITEM`;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        responseObj.new_array = rows.map(row => row.ITEM);

        logger.logInfo(`Retrieved ${responseObj.new_array.length} filtered items`, 'getUniqueCategoriesDb2');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getUniqueCategoriesDb2:', error);
    callback(error, null);
  }
}

// Get unique part numbers
async function getUniquePns(values, callback) {
  let responseObj = {
    status_res: "success",
    all_pns: []
  };

  try {
    logger.logInfo('Getting unique part numbers', 'getUniquePns');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    let sql = `
      SELECT DISTINCT a.PART_NUM
      FROM QODS.GEM_DEFECT a
      WHERE a.PART_NUM IS NOT NULL AND TRIM(a.PART_NUM) != ''
      ORDER BY a.PART_NUM
      FETCH FIRST 1000 ROWS ONLY
    `;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        responseObj.all_pns = rows.map(row => row.PART_NUM);

        logger.logInfo(`Retrieved ${responseObj.all_pns.length} part numbers`, 'getUniquePns');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getUniquePns:', error);
    callback(error, null);
  }
}

// Get combo defrate data with targets from Excel
async function getComboDefrateData(values, callback) {
  let responseObj = {
    status_res: "success",
    chart_data: [],
    table_data: [],
    chart_type: "combo"
  };

  try {
    const { startDate, endDate, suppliers, partNumbers, vintages } = values;

    logger.logInfo(`Getting combo defrate data from ${startDate} to ${endDate}`, 'getComboDefrateData');

    // First, read target rates from breakout_targets.xlsx
    const targetsFilePath = path.join(__dirname, '../excel/breakout_targets.xlsx');
    let targetRates = {};

    if (fs.existsSync(targetsFilePath)) {
      try {
        const workbook = xlsx.readFile(targetsFilePath);
        const sheetName = workbook.SheetNames[0]; // First sheet contains targets
        const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

        sheet.forEach(row => {
          const breakoutGroup = row['Breakout Group'];
          const targetRate = row['Target Rate'];
          if (breakoutGroup && targetRate !== undefined) {
            targetRates[breakoutGroup] = targetRate;
          }
        });

        logger.logInfo(`Loaded ${Object.keys(targetRates).length} target rates from Excel`, 'getComboDefrateData');
      } catch (excelError) {
        logger.logError('Error reading targets Excel file:', excelError);
      }
    }

    // Get database connection
    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    // Build SQL query
    let sql = `
      SELECT
        a.PART_NUM,
        YEAR(a.INCIDENT_DATE) as YEAR,
        MONTH(a.INCIDENT_DATE) as MONTH,
        CONCAT(YEAR(a.INCIDENT_DATE), '-', LPAD(MONTH(a.INCIDENT_DATE), 2, '0')) as YEAR_MONTH,
        COUNT(*) as DEFECT_COUNT,
        COUNT(DISTINCT a.DEFECT_ID) as TOTAL_DEFECTS
      FROM QODS.GEM_DEFECT a
      WHERE a.INCIDENT_DATE BETWEEN ? AND ?
    `;

    let queryParams = [startDate, endDate];

    // Add filters
    if (partNumbers && partNumbers.length > 0) {
      const pnPlaceholders = partNumbers.map(() => '?').join(',');
      sql += ` AND a.PART_NUM IN (${pnPlaceholders})`;
      queryParams = queryParams.concat(partNumbers);
    }

    if (suppliers && suppliers.length > 0) {
      const supplierPlaceholders = suppliers.map(() => '?').join(',');
      sql += ` AND a.SUPPLIER IN (${supplierPlaceholders})`;
      queryParams = queryParams.concat(suppliers);
    }

    if (vintages && vintages.length > 0) {
      const vintagePlaceholders = vintages.map(() => '?').join(',');
      sql += ` AND a.VINTAGE IN (${vintagePlaceholders})`;
      queryParams = queryParams.concat(vintages);
    }

    sql += ` GROUP BY a.PART_NUM, YEAR(a.INCIDENT_DATE), MONTH(a.INCIDENT_DATE)
             ORDER BY a.PART_NUM, YEAR(a.INCIDENT_DATE), MONTH(a.INCIDENT_DATE)`;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, queryParams, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        // Process data for combo chart
        const chartData = [];
        const tableData = [];

        rows.forEach(row => {
          const partNum = row.PART_NUM;
          const yearMonth = row.YEAR_MONTH;
          const defectCount = row.DEFECT_COUNT;

          // Calculate fail rate (assuming volume data would come from another source)
          // For now, using a mock volume calculation
          const mockVolume = Math.floor(Math.random() * 10000) + 1000;
          const failRate = (defectCount / mockVolume) * 100;

          // Get target rate for this part (would need mapping to breakout group)
          const targetRate = 0.5; // Default target rate as percentage

          // Add to chart data
          chartData.push({
            group: 'Fail Rate',
            key: yearMonth,
            value: failRate,
            partNum: partNum
          });

          chartData.push({
            group: 'Target Rate',
            key: yearMonth,
            value: targetRate,
            partNum: partNum
          });

          // Add to table data
          tableData.push({
            partNumber: partNum,
            month: yearMonth,
            defectCount: defectCount,
            volume: mockVolume,
            failRate: failRate.toFixed(2),
            targetRate: targetRate.toFixed(2),
            status: failRate > targetRate ? 'Above Target' : 'Within Target'
          });
        });

        responseObj.chart_data = chartData;
        responseObj.table_data = tableData;

        logger.logInfo(`Retrieved ${rows.length} data points for combo chart`, 'getComboDefrateData');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getComboDefrateData:', error);
    callback(error, null);
  }
}

// Get unique suppliers
async function getSuppliers(values, callback) {
  let responseObj = {
    status_res: "success",
    suppliers: []
  };

  try {
    logger.logInfo('Getting unique suppliers', 'getSuppliers');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    let sql = `
      SELECT DISTINCT a.SUPPLIER
      FROM QODS.GEM_DEFECT a
      WHERE a.SUPPLIER IS NOT NULL AND TRIM(a.SUPPLIER) != ''
      ORDER BY a.SUPPLIER
      FETCH FIRST 500 ROWS ONLY
    `;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        responseObj.suppliers = rows.map(row => row.SUPPLIER);

        logger.logInfo(`Retrieved ${responseObj.suppliers.length} suppliers`, 'getSuppliers');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getSuppliers:', error);
    callback(error, null);
  }
}

// Get unique vintages
async function getVintages(values, callback) {
  let responseObj = {
    status_res: "success",
    vintages: []
  };

  try {
    logger.logInfo('Getting unique vintages', 'getVintages');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    let sql = `
      SELECT DISTINCT a.VINTAGE
      FROM QODS.GEM_DEFECT a
      WHERE a.VINTAGE IS NOT NULL AND TRIM(a.VINTAGE) != ''
      ORDER BY a.VINTAGE
      FETCH FIRST 500 ROWS ONLY
    `;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        responseObj.vintages = rows.map(row => row.VINTAGE);

        logger.logInfo(`Retrieved ${responseObj.vintages.length} vintages`, 'getVintages');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getVintages:', error);
    callback(error, null);
  }
}

// Get comparison data for two part numbers
async function getCompareData(values, callback) {
  let responseObj = {
    status_res: "success",
    comparison_data: [],
    chart_data: []
  };

  try {
    const { partNumber1, partNumber2, startDate, endDate } = values;

    logger.logInfo(`Comparing performance between ${partNumber1} and ${partNumber2}`, 'getCompareData');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    let sql = `
      SELECT
        a.PART_NUM,
        YEAR(a.INCIDENT_DATE) as YEAR,
        MONTH(a.INCIDENT_DATE) as MONTH,
        CONCAT(YEAR(a.INCIDENT_DATE), '-', LPAD(MONTH(a.INCIDENT_DATE), 2, '0')) as YEAR_MONTH,
        COUNT(*) as DEFECT_COUNT,
        TRIM(b.ROOT_CAUSE_1) as ROOT_CAUSE
      FROM QODS.GEM_DEFECT a
      LEFT JOIN QEVAL.GEM_DEFECT_UPDATES b ON a.DEFECT_ID = b.DEFECT_ID
      WHERE a.PART_NUM IN (?, ?)
    `;

    let queryParams = [partNumber1, partNumber2];

    if (startDate && endDate) {
      sql += ` AND a.INCIDENT_DATE BETWEEN ? AND ?`;
      queryParams.push(startDate, endDate);
    }

    sql += ` GROUP BY a.PART_NUM, YEAR(a.INCIDENT_DATE), MONTH(a.INCIDENT_DATE), TRIM(b.ROOT_CAUSE_1)
             ORDER BY a.PART_NUM, YEAR(a.INCIDENT_DATE), MONTH(a.INCIDENT_DATE)`;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, queryParams, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        // Process data for comparison
        const comparisonData = {};
        const chartData = [];

        rows.forEach(row => {
          const partNum = row.PART_NUM;
          const yearMonth = row.YEAR_MONTH;
          const defectCount = row.DEFECT_COUNT;
          const rootCause = row.ROOT_CAUSE || 'Unknown';

          if (!comparisonData[partNum]) {
            comparisonData[partNum] = {
              partNumber: partNum,
              totalDefects: 0,
              monthlyData: {},
              rootCauses: {}
            };
          }

          comparisonData[partNum].totalDefects += defectCount;

          if (!comparisonData[partNum].monthlyData[yearMonth]) {
            comparisonData[partNum].monthlyData[yearMonth] = 0;
          }
          comparisonData[partNum].monthlyData[yearMonth] += defectCount;

          if (!comparisonData[partNum].rootCauses[rootCause]) {
            comparisonData[partNum].rootCauses[rootCause] = 0;
          }
          comparisonData[partNum].rootCauses[rootCause] += defectCount;

          // Add to chart data
          chartData.push({
            group: partNum,
            key: yearMonth,
            value: defectCount
          });
        });

        responseObj.comparison_data = Object.values(comparisonData);
        responseObj.chart_data = chartData;

        logger.logInfo(`Retrieved comparison data for ${Object.keys(comparisonData).length} part numbers`, 'getCompareData');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getCompareData:', error);
    callback(error, null);
  }
}
