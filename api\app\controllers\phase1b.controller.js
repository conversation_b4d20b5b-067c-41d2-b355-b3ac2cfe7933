const configSwitch = require("./config.js");
const ibmdb = require("ibm_db");
const path = require('path');
const xlsx = require('xlsx');
const fs = require('fs');
const logger = require('../utils/logger');

// Export controller functions
exports.get_fail_count_by_category_db = (req, res) => {
  getFailCountByCategoryDb(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_fail_count_by_category_db:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_unique_categories_db = (req, res) => {
  getUniqueCategoriesDb(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_unique_categories_db:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_unique_categories_db2 = (req, res) => {
  getUniqueCategoriesDb2(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_unique_categories_db2:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_unique_pns = (req, res) => {
  getUniquePns(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_unique_pns:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_combo_defrate_data = (req, res) => {
  getComboDefrateData(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_combo_defrate_data:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_suppliers = (req, res) => {
  getSuppliers(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_suppliers:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_vintages = (req, res) => {
  getVintages(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_vintages:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_compare_data = (req, res) => {
  getCompareData(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_compare_data:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_groups = (req, res) => {
  getGroups(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_groups:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_sectors = (req, res) => {
  getSectors(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_sectors:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

// Helper function to get database connection
function getDbConnection() {
  const config = configSwitch.config();
  return config.db_connection_string;
}

// Get fail count by category from database
async function getFailCountByCategoryDb(values, callback) {
  let responseObj = {
    status_res: "success",
    counts_by_period: [],
    full_data: []
  };

  try {
    const { PN, category, area, startDate, endDate } = values;
    
    logger.logInfo(`Getting fail count for PN: ${PN}, Category: ${category}, Area: ${area}`, 'getFailCountByCategoryDb');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    let sql = `
      SELECT 
        YEAR(a.INCIDENT_DATE) as YEAR,
        MONTH(a.INCIDENT_DATE) as MONTH,
        CONCAT(YEAR(a.INCIDENT_DATE), '-', LPAD(MONTH(a.INCIDENT_DATE), 2, '0')) as YEAR_MONTH,
        COUNT(*) as DEFECT_COUNT
      FROM QODS.GEM_DEFECT a
      LEFT JOIN QEVAL.GEM_DEFECT_UPDATES b ON a.DEFECT_ID = b.DEFECT_ID
      WHERE 1=1
    `;

    let queryParams = [];

    if (PN) {
      sql += ` AND a.PART_NUM = ?`;
      queryParams.push(PN);
    }

    if (category) {
      sql += ` AND TRIM(b.ROOT_CAUSE_1) = ?`;
      queryParams.push(category);
    }

    if (area) {
      sql += ` AND a.PROCESS = ?`;
      queryParams.push(area);
    }

    if (startDate && endDate) {
      sql += ` AND a.INCIDENT_DATE BETWEEN ? AND ?`;
      queryParams.push(startDate, endDate);
    }

    sql += ` GROUP BY YEAR(a.INCIDENT_DATE), MONTH(a.INCIDENT_DATE)
             ORDER BY YEAR(a.INCIDENT_DATE), MONTH(a.INCIDENT_DATE)`;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, queryParams, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        responseObj.full_data = rows;
        responseObj.counts_by_period = rows.map(row => ({
          key: row.YEAR_MONTH,
          value: row.DEFECT_COUNT,
          group: "Defect Count"
        }));

        logger.logInfo(`Retrieved ${rows.length} data points`, 'getFailCountByCategoryDb');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getFailCountByCategoryDb:', error);
    callback(error, null);
  }
}

// Get unique categories from database
async function getUniqueCategoriesDb(values, callback) {
  let responseObj = {
    status_res: "success",
    all_categories: [],
    all_stages: []
  };

  try {
    const { PN } = values;
    
    logger.logInfo(`Getting unique categories for PN: ${PN}`, 'getUniqueCategoriesDb');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    // Get categories
    let categorySql = `
      SELECT DISTINCT TRIM(b.ROOT_CAUSE_1) as CATEGORY
      FROM QODS.GEM_DEFECT a
      LEFT JOIN QEVAL.GEM_DEFECT_UPDATES b ON a.DEFECT_ID = b.DEFECT_ID
      WHERE b.ROOT_CAUSE_1 IS NOT NULL AND TRIM(b.ROOT_CAUSE_1) != ''
    `;

    // Get stages
    let stageSql = `
      SELECT DISTINCT a.PROCESS as STAGE
      FROM QODS.GEM_DEFECT a
      WHERE a.PROCESS IS NOT NULL AND TRIM(a.PROCESS) != ''
    `;

    if (PN) {
      categorySql += ` AND a.PART_NUM = '${PN}'`;
      stageSql += ` AND a.PART_NUM = '${PN}'`;
    }

    categorySql += ` ORDER BY CATEGORY`;
    stageSql += ` ORDER BY STAGE`;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      // Get categories
      conn.query(categorySql, function(err, categoryRows) {
        if (err) {
          logger.logError('Database query error for categories:', err);
          conn.close();
          callback(err, null);
          return;
        }

        responseObj.all_categories = categoryRows.map(row => row.CATEGORY);

        // Get stages
        conn.query(stageSql, function(err, stageRows) {
          if (err) {
            logger.logError('Database query error for stages:', err);
            conn.close();
            callback(err, null);
            return;
          }

          responseObj.all_stages = stageRows.map(row => row.STAGE);

          logger.logInfo(`Retrieved ${responseObj.all_categories.length} categories and ${responseObj.all_stages.length} stages`, 'getUniqueCategoriesDb');
          conn.close();
          callback(null, responseObj);
        });
      });
    });

  } catch (error) {
    logger.logError('Error in getUniqueCategoriesDb:', error);
    callback(error, null);
  }
}

// Get unique categories with additional filtering
async function getUniqueCategoriesDb2(values, callback) {
  let responseObj = {
    status_res: "success",
    new_array: []
  };

  try {
    const { PN, area, category } = values;
    
    logger.logInfo(`Getting filtered categories for PN: ${PN}, Area: ${area}, Category: ${category}`, 'getUniqueCategoriesDb2');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    let sql = '';
    
    if (area) {
      // Get categories for the specified area
      sql = `
        SELECT DISTINCT TRIM(b.ROOT_CAUSE_1) as ITEM
        FROM QODS.GEM_DEFECT a
        LEFT JOIN QEVAL.GEM_DEFECT_UPDATES b ON a.DEFECT_ID = b.DEFECT_ID
        WHERE b.ROOT_CAUSE_1 IS NOT NULL AND TRIM(b.ROOT_CAUSE_1) != ''
        AND a.PROCESS = '${area}'
      `;
    } else if (category) {
      // Get areas for the specified category
      sql = `
        SELECT DISTINCT a.PROCESS as ITEM
        FROM QODS.GEM_DEFECT a
        LEFT JOIN QEVAL.GEM_DEFECT_UPDATES b ON a.DEFECT_ID = b.DEFECT_ID
        WHERE a.PROCESS IS NOT NULL AND TRIM(a.PROCESS) != ''
        AND TRIM(b.ROOT_CAUSE_1) = '${category}'
      `;
    }

    if (PN) {
      sql += ` AND a.PART_NUM = '${PN}'`;
    }

    sql += ` ORDER BY ITEM`;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        responseObj.new_array = rows.map(row => row.ITEM);

        logger.logInfo(`Retrieved ${responseObj.new_array.length} filtered items`, 'getUniqueCategoriesDb2');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getUniqueCategoriesDb2:', error);
    callback(error, null);
  }
}

// Get unique part numbers
async function getUniquePns(values, callback) {
  let responseObj = {
    status_res: "success",
    all_pns: []
  };

  try {
    logger.logInfo('Getting unique part numbers', 'getUniquePns');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    let sql = `
      SELECT DISTINCT a.PART_NUM
      FROM QODS.GEM_DEFECT a
      WHERE a.PART_NUM IS NOT NULL AND TRIM(a.PART_NUM) != ''
      ORDER BY a.PART_NUM
      FETCH FIRST 1000 ROWS ONLY
    `;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        responseObj.all_pns = rows.map(row => row.PART_NUM);

        logger.logInfo(`Retrieved ${responseObj.all_pns.length} part numbers`, 'getUniquePns');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getUniquePns:', error);
    callback(error, null);
  }
}

// Get combo defrate data with targets from Excel
async function getComboDefrateData(values, callback) {
  let responseObj = {
    status_res: "success",
    chart_data: [],
    table_data: [],
    chart_type: "combo"
  };

  try {
    const { startDate, endDate, viewBy, groups, partNumbers, sectors, vintages, suppliers } = values;

    logger.logInfo(`Getting combo defrate data from ${startDate} to ${endDate}`, 'getComboDefrateData');
    logger.logInfo(`View by: ${viewBy ? viewBy.join(', ') : 'none'}`, 'getComboDefrateData');

    // Step 1: Determine part numbers to analyze
    let partNumbersToAnalyze = [];

    if (groups && groups.length > 0) {
      // Get part numbers from groups using new_metis_test.xlsx
      const metisFilePath = path.join(__dirname, '../excel/new_metis_test.xlsx');
      if (fs.existsSync(metisFilePath)) {
        const workbook = xlsx.readFile(metisFilePath);
        const sheetName = workbook.SheetNames[0];
        const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

        const filteredRows = sheet.filter(row =>
          groups.includes(row['Full Breakout Name'])
        );
        partNumbersToAnalyze = [...new Set(filteredRows.map(item => item.PN).filter(pn => pn))];
        logger.logInfo(`Found ${partNumbersToAnalyze.length} part numbers from ${groups.length} groups`, 'getComboDefrateData');
      }
    } else if (partNumbers && partNumbers.length > 0) {
      // Use provided part numbers
      partNumbersToAnalyze = partNumbers;
      logger.logInfo(`Using ${partNumbersToAnalyze.length} provided part numbers`, 'getComboDefrateData');
    } else {
      // If no specific filters, get a sample of part numbers
      partNumbersToAnalyze = await getSamplePartNumbers();
      logger.logInfo(`Using ${partNumbersToAnalyze.length} sample part numbers`, 'getComboDefrateData');
    }

    if (partNumbersToAnalyze.length === 0) {
      throw new Error('No part numbers found for analysis');
    }

    // Step 2: Read target rates from breakout_targets.xlsx
    const targetsFilePath = path.join(__dirname, '../excel/breakout_targets.xlsx');
    let targetRates = {};

    if (fs.existsSync(targetsFilePath)) {
      try {
        const workbook = xlsx.readFile(targetsFilePath);
        const sheetName = workbook.SheetNames[0]; // First sheet contains targets
        const sheet = xlsx.utils.sheet_to_json(workbook.Sheets[sheetName]);

        sheet.forEach(row => {
          const breakoutGroup = row['Breakout Group'];
          const targetRate = row['Target Rate'];
          if (breakoutGroup && targetRate !== undefined) {
            targetRates[breakoutGroup] = targetRate;
          }
        });

        logger.logInfo(`Loaded ${Object.keys(targetRates).length} target rates from Excel`, 'getComboDefrateData');
      } catch (excelError) {
        logger.logError('Error reading targets Excel file:', excelError);
      }
    }

    // Step 3: Build database query based on viewBy parameters
    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    // Format part numbers for SQL query
    const formattedPNs = partNumbersToAnalyze.map(pn => String(pn));
    const pnPlaceholders = formattedPNs.map(() => '?').join(', ');

    // Build different queries based on viewBy parameter

    // Process each viewBy option
    const chartData = [];
    const tableData = [];

    for (const view of viewBy) {
      let sql = '';
      let params = [];

      if (view === 'sector') {
        sql = `
          SELECT
            SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) AS MONTH,
            COALESCE(TRIM(a.PROCESS), 'UNKNOWN') AS CATEGORY,
            COUNT(*) AS DEFECT_COUNT
          FROM QODS.GEM_DEFECT AS a
          WHERE a.PART_NUM IN (${pnPlaceholders})
            AND SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) BETWEEN ? AND ?
        `;
        params = [...formattedPNs, startDate.substring(0, 7), endDate.substring(0, 7)];

        // Add sector filter if specified
        if (sectors && sectors.length > 0) {
          const sectorPlaceholders = sectors.map(() => '?').join(',');
          sql += ` AND a.PROCESS IN (${sectorPlaceholders})`;
          params = params.concat(sectors);
        }

        sql += `
          GROUP BY SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7), COALESCE(TRIM(a.PROCESS), 'UNKNOWN')
          ORDER BY MONTH, DEFECT_COUNT DESC
        `;

      } else if (view === 'supplier') {
        sql = `
          SELECT
            SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) AS MONTH,
            CASE
              WHEN LENGTH(TRIM(a.PART_SER)) >= 6 THEN
                COALESCE(SUBSTR(TRIM(a.PART_SER), 5, 2), 'UNKNOWN')
              ELSE 'UNKNOWN'
            END AS CATEGORY,
            COUNT(*) AS DEFECT_COUNT
          FROM QODS.GEM_DEFECT AS a
          WHERE a.PART_NUM IN (${pnPlaceholders})
            AND a.PROCESS = 'FULL'
            AND SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) BETWEEN ? AND ?
        `;
        params = [...formattedPNs, startDate.substring(0, 7), endDate.substring(0, 7)];

        // Add supplier filter if specified
        if (suppliers && suppliers.length > 0) {
          const supplierConditions = suppliers.map(() =>
            "SUBSTR(TRIM(a.PART_SER), 5, 2) = ?"
          ).join(' OR ');
          sql += ` AND (${supplierConditions})`;
          params = params.concat(suppliers);
        }

        sql += `
          GROUP BY SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7),
                   CASE WHEN LENGTH(TRIM(a.PART_SER)) >= 6 THEN
                     COALESCE(SUBSTR(TRIM(a.PART_SER), 5, 2), 'UNKNOWN')
                   ELSE 'UNKNOWN' END
          ORDER BY MONTH, DEFECT_COUNT DESC
        `;

      } else if (view === 'vintage') {
        sql = `
          SELECT
            SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) AS MONTH,
            CASE
              WHEN LENGTH(TRIM(a.PART_SER)) >= 8 THEN
                COALESCE(SUBSTR(TRIM(a.PART_SER), 7, 2), 'UNKNOWN')
              ELSE 'UNKNOWN'
            END AS CATEGORY,
            COUNT(*) AS DEFECT_COUNT
          FROM QODS.GEM_DEFECT AS a
          WHERE a.PART_NUM IN (${pnPlaceholders})
            AND a.PROCESS = 'FULL'
            AND SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) BETWEEN ? AND ?
        `;
        params = [...formattedPNs, startDate.substring(0, 7), endDate.substring(0, 7)];

        // Add vintage filter if specified
        if (vintages && vintages.length > 0) {
          const vintageConditions = vintages.map(() =>
            "SUBSTR(TRIM(a.PART_SER), 7, 2) = ?"
          ).join(' OR ');
          sql += ` AND (${vintageConditions})`;
          params = params.concat(vintages);
        }

        sql += `
          GROUP BY SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7),
                   CASE WHEN LENGTH(TRIM(a.PART_SER)) >= 8 THEN
                     COALESCE(SUBSTR(TRIM(a.PART_SER), 7, 2), 'UNKNOWN')
                   ELSE 'UNKNOWN' END
          ORDER BY MONTH, DEFECT_COUNT DESC
        `;

      } else if (view === 'partNumber') {
        sql = `
          SELECT
            SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) AS MONTH,
            CASE
              WHEN LENGTH(TRIM(a.PART_NUM)) >= 7 THEN
                RIGHT(TRIM(a.PART_NUM), 7)
              ELSE COALESCE(TRIM(a.PART_NUM), 'UNKNOWN')
            END AS CATEGORY,
            COUNT(*) AS DEFECT_COUNT
          FROM QODS.GEM_DEFECT AS a
          WHERE a.PART_NUM IN (${pnPlaceholders})
            AND a.PROCESS = 'FULL'
            AND SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) BETWEEN ? AND ?
        `;
        params = [...formattedPNs, startDate.substring(0, 7), endDate.substring(0, 7)];

        sql += `
          GROUP BY SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7),
                   CASE WHEN LENGTH(TRIM(a.PART_NUM)) >= 7 THEN
                     RIGHT(TRIM(a.PART_NUM), 7)
                   ELSE COALESCE(TRIM(a.PART_NUM), 'UNKNOWN') END
          ORDER BY MONTH, DEFECT_COUNT DESC
        `;

      } else {
        // Default to group view - aggregate by breakout groups
        sql = `
          SELECT
            SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) AS MONTH,
            'GROUP_ANALYSIS' AS CATEGORY,
            COUNT(*) AS DEFECT_COUNT
          FROM QODS.GEM_DEFECT AS a
          WHERE a.PART_NUM IN (${pnPlaceholders})
            AND a.PROCESS = 'FULL'
            AND SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7) BETWEEN ? AND ?
          GROUP BY SUBSTR(CHAR(a.INCIDENT_DATE, ISO), 1, 7)
          ORDER BY MONTH
        `;
        params = [...formattedPNs, startDate.substring(0, 7), endDate.substring(0, 7)];
      }

      // Execute query for this view
      ibmdb.open(connString, function(err, conn) {
        if (err) {
          logger.logError('Database connection error:', err);
          callback(err, null);
          return;
        }

        conn.query(sql, params, function(err, rows) {
          if (err) {
            logger.logError('Database query error:', err);
            conn.close();
            callback(err, null);
            return;
          }

          // Process data for this view
          rows.forEach(row => {
            const month = row.MONTH;
            const category = row.CATEGORY;
            const defectCount = row.DEFECT_COUNT;

            // Calculate fail rate (using mock volume for now)
            const mockVolume = Math.floor(Math.random() * 10000) + 1000;
            const failRate = (defectCount / mockVolume) * 100;

            // Get target rate (default for now)
            const targetRate = 0.5;

            // Add to chart data
            chartData.push({
              group: `${view}_${category}`,
              key: month,
              value: failRate,
              view: view,
              category: category
            });

            // Add to table data
            tableData.push({
              view: view,
              category: category,
              month: month,
              defectCount: defectCount,
              volume: mockVolume,
              failRate: failRate.toFixed(2),
              targetRate: targetRate.toFixed(2),
              status: failRate > targetRate ? 'Above Target' : 'Within Target'
            });
          });

          conn.close();
        });
      });
    }

    // Return the aggregated results
    responseObj.chart_data = chartData;
    responseObj.table_data = tableData;

    logger.logInfo(`Retrieved data for ${viewBy.length} views`, 'getComboDefrateData');
    callback(null, responseObj);

  } catch (error) {
    logger.logError('Error in getComboDefrateData:', error);
    callback(error, null);
  }
}

// Helper function to get sample part numbers
async function getSamplePartNumbers() {
  try {
    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    return new Promise((resolve, reject) => {
      ibmdb.open(connString, function(err, conn) {
        if (err) {
          reject(err);
          return;
        }

        const sql = `
          SELECT DISTINCT a.PART_NUM
          FROM QODS.GEM_DEFECT a
          WHERE a.PART_NUM IS NOT NULL AND TRIM(a.PART_NUM) != ''
          ORDER BY a.PART_NUM
          FETCH FIRST 50 ROWS ONLY
        `;

        conn.query(sql, function(err, rows) {
          conn.close();
          if (err) {
            reject(err);
            return;
          }
          resolve(rows.map(row => row.PART_NUM));
        });
      });
    });
  } catch (error) {
    logger.logError('Error getting sample part numbers:', error);
    return [];
  }
}

// Get unique suppliers
async function getSuppliers(values, callback) {
  let responseObj = {
    status_res: "success",
    suppliers: []
  };

  try {
    logger.logInfo('Getting unique suppliers', 'getSuppliers');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    let sql = `
      SELECT DISTINCT a.SUPPLIER
      FROM QODS.GEM_DEFECT a
      WHERE a.SUPPLIER IS NOT NULL AND TRIM(a.SUPPLIER) != ''
      ORDER BY a.SUPPLIER
      FETCH FIRST 500 ROWS ONLY
    `;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        responseObj.suppliers = rows.map(row => row.SUPPLIER);

        logger.logInfo(`Retrieved ${responseObj.suppliers.length} suppliers`, 'getSuppliers');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getSuppliers:', error);
    callback(error, null);
  }
}

// Get unique vintages
async function getVintages(values, callback) {
  let responseObj = {
    status_res: "success",
    vintages: []
  };

  try {
    logger.logInfo('Getting unique vintages', 'getVintages');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    let sql = `
      SELECT DISTINCT a.VINTAGE
      FROM QODS.GEM_DEFECT a
      WHERE a.VINTAGE IS NOT NULL AND TRIM(a.VINTAGE) != ''
      ORDER BY a.VINTAGE
      FETCH FIRST 500 ROWS ONLY
    `;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        responseObj.vintages = rows.map(row => row.VINTAGE);

        logger.logInfo(`Retrieved ${responseObj.vintages.length} vintages`, 'getVintages');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getVintages:', error);
    callback(error, null);
  }
}

// Get comparison data for two part numbers
async function getCompareData(values, callback) {
  let responseObj = {
    status_res: "success",
    comparison_data: [],
    chart_data: []
  };

  try {
    const { partNumber1, partNumber2, startDate, endDate } = values;

    logger.logInfo(`Comparing performance between ${partNumber1} and ${partNumber2}`, 'getCompareData');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    let sql = `
      SELECT
        a.PART_NUM,
        YEAR(a.INCIDENT_DATE) as YEAR,
        MONTH(a.INCIDENT_DATE) as MONTH,
        CONCAT(YEAR(a.INCIDENT_DATE), '-', LPAD(MONTH(a.INCIDENT_DATE), 2, '0')) as YEAR_MONTH,
        COUNT(*) as DEFECT_COUNT,
        TRIM(b.ROOT_CAUSE_1) as ROOT_CAUSE
      FROM QODS.GEM_DEFECT a
      LEFT JOIN QEVAL.GEM_DEFECT_UPDATES b ON a.DEFECT_ID = b.DEFECT_ID
      WHERE a.PART_NUM IN (?, ?)
    `;

    let queryParams = [partNumber1, partNumber2];

    if (startDate && endDate) {
      sql += ` AND a.INCIDENT_DATE BETWEEN ? AND ?`;
      queryParams.push(startDate, endDate);
    }

    sql += ` GROUP BY a.PART_NUM, YEAR(a.INCIDENT_DATE), MONTH(a.INCIDENT_DATE), TRIM(b.ROOT_CAUSE_1)
             ORDER BY a.PART_NUM, YEAR(a.INCIDENT_DATE), MONTH(a.INCIDENT_DATE)`;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, queryParams, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        // Process data for comparison
        const comparisonData = {};
        const chartData = [];

        rows.forEach(row => {
          const partNum = row.PART_NUM;
          const yearMonth = row.YEAR_MONTH;
          const defectCount = row.DEFECT_COUNT;
          const rootCause = row.ROOT_CAUSE || 'Unknown';

          if (!comparisonData[partNum]) {
            comparisonData[partNum] = {
              partNumber: partNum,
              totalDefects: 0,
              monthlyData: {},
              rootCauses: {}
            };
          }

          comparisonData[partNum].totalDefects += defectCount;

          if (!comparisonData[partNum].monthlyData[yearMonth]) {
            comparisonData[partNum].monthlyData[yearMonth] = 0;
          }
          comparisonData[partNum].monthlyData[yearMonth] += defectCount;

          if (!comparisonData[partNum].rootCauses[rootCause]) {
            comparisonData[partNum].rootCauses[rootCause] = 0;
          }
          comparisonData[partNum].rootCauses[rootCause] += defectCount;

          // Add to chart data
          chartData.push({
            group: partNum,
            key: yearMonth,
            value: defectCount
          });
        });

        responseObj.comparison_data = Object.values(comparisonData);
        responseObj.chart_data = chartData;

        logger.logInfo(`Retrieved comparison data for ${Object.keys(comparisonData).length} part numbers`, 'getCompareData');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getCompareData:', error);
    callback(error, null);
  }
}

// Get unique groups from new_metis_test.xlsx
async function getGroups(values, callback) {
  let responseObj = {
    status_res: "success",
    groups: []
  };

  try {
    logger.logInfo('Getting unique groups from Excel file', 'getGroups');

    // Read groups from new_metis_test.xlsx
    const excelPath = path.join(__dirname, '../excel/new_metis_test.xlsx');

    if (!fs.existsSync(excelPath)) {
      logger.logError('Excel file not found: new_metis_test.xlsx', null, 'getGroups');
      callback(new Error('Excel file not found'), null);
      return;
    }

    const workbook = xlsx.readFile(excelPath);
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const jsonData = xlsx.utils.sheet_to_json(sheet);

    // Get unique Full Breakout Names as groups
    const uniqueGroups = [...new Set(jsonData
      .map(row => row['Full Breakout Name'])
      .filter(name => name && name.trim() !== '' && name.trim() !== ' ')
    )];

    responseObj.groups = uniqueGroups;

    logger.logInfo(`Retrieved ${responseObj.groups.length} groups from Excel file`, 'getGroups');
    callback(null, responseObj);

  } catch (error) {
    logger.logError('Error in getGroups:', error);
    callback(error, null);
  }
}

// Get unique sectors from database
async function getSectors(values, callback) {
  let responseObj = {
    status_res: "success",
    sectors: []
  };

  try {
    logger.logInfo('Getting unique sectors', 'getSectors');

    const config = configSwitch.config('R0ADB2');
    let connString = "DRIVER={DB2};"
      + "DATABASE=" + 'R0ADB2' + ";"
      + "UID=" + config.database_user + ";"
      + "PWD=" + config.database_password + ";"
      + "HOSTNAME=" + config.database_host + ";"
      + "PORT=" + config.database_port;

    let sql = `
      SELECT DISTINCT a.PROCESS as SECTOR
      FROM QODS.GEM_DEFECT a
      WHERE a.PROCESS IS NOT NULL AND TRIM(a.PROCESS) != ''
      ORDER BY a.PROCESS
      FETCH FIRST 100 ROWS ONLY
    `;

    ibmdb.open(connString, function(err, conn) {
      if (err) {
        logger.logError('Database connection error:', err);
        callback(err, null);
        return;
      }

      conn.query(sql, function(err, rows) {
        if (err) {
          logger.logError('Database query error:', err);
          conn.close();
          callback(err, null);
          return;
        }

        responseObj.sectors = rows.map(row => row.SECTOR);

        logger.logInfo(`Retrieved ${responseObj.sectors.length} sectors`, 'getSectors');
        conn.close();
        callback(null, responseObj);
      });
    });

  } catch (error) {
    logger.logError('Error in getSectors:', error);
    callback(error, null);
  }
}
