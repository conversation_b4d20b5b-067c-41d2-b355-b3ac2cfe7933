{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\LineBarComboChart.vue?vue&type=style&index=0&id=e8088f62&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\LineBarComboChart.vue", "mtime": 1746725782258}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5saW5lLWJhci1jb21iby1jaGFydC1jb250YWluZXIgewogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICB3aWR0aDogMTAwJTsKfQoKLmxvYWRpbmctaW5kaWNhdG9yIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBoZWlnaHQ6IDEwMCU7CiAgbWluLWhlaWdodDogMjAwcHg7Cn0KCi5sb2FkaW5nLXNwaW5uZXIgewogIGJvcmRlcjogNHB4IHNvbGlkIHJnYmEoMCwgMCwgMCwgMC4xKTsKICBib3JkZXItcmFkaXVzOiA1MCU7CiAgYm9yZGVyLXRvcDogNHB4IHNvbGlkICMwZjYyZmU7CiAgd2lkdGg6IDMwcHg7CiAgaGVpZ2h0OiAzMHB4OwogIGFuaW1hdGlvbjogc3BpbiAxcyBsaW5lYXIgaW5maW5pdGU7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKfQoKQGtleWZyYW1lcyBzcGluIHsKICAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOyB9CiAgMTAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7IH0KfQoKLm5vLWRhdGEtbWVzc2FnZSB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGhlaWdodDogMTAwJTsKICBtaW4taGVpZ2h0OiAyMDBweDsKICBjb2xvcjogIzY2NjsKICBmb250LXN0eWxlOiBpdGFsaWM7Cn0KCi5jaGFydC1jb250YWluZXIgewogIHdpZHRoOiAxMDAlOwp9Cg=="}, {"version": 3, "sources": ["LineBarComboChart.vue"], "names": [], "mappings": ";AAoGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "LineBarComboChart.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n  <div class=\"line-bar-combo-chart-container\">\n    <div v-if=\"loading\" class=\"loading-indicator\">\n      <div class=\"loading-spinner\"></div>\n      <span>Loading chart data...</span>\n    </div>\n    <div v-else-if=\"!data || data.length === 0\" class=\"no-data-message\">\n      No data available for the chart\n    </div>\n    <div v-else ref=\"chartContainer\" class=\"chart-container\" :style=\"{ height }\"></div>\n  </div>\n</template>\n\n<script>\nimport { ComboChart } from '@carbon/charts';\nimport '@carbon/charts/styles.css';\n\nexport default {\n  name: 'LineBarComboChart',\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    options: {\n      type: Object,\n      default: () => ({})\n    },\n    height: {\n      type: String,\n      default: '400px'\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      chart: null\n    };\n  },\n  watch: {\n    data: {\n      handler() {\n        this.updateChart();\n      },\n      deep: true\n    },\n    options: {\n      handler() {\n        this.updateChart();\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    this.initChart();\n  },\n  beforeDestroy() {\n    if (this.chart) {\n      this.chart.destroy();\n    }\n  },\n  methods: {\n    initChart() {\n      if (!this.data || this.data.length === 0) return;\n\n      const chartContainer = this.$refs.chartContainer;\n      if (!chartContainer) return;\n\n      // Initialize the chart\n      this.chart = new ComboChart(chartContainer, {\n        data: this.data,\n        options: this.options\n      });\n    },\n    updateChart() {\n      if (!this.chart) {\n        this.initChart();\n        return;\n      }\n\n      if (!this.data || this.data.length === 0) {\n        if (this.chart) {\n          this.chart.destroy();\n          this.chart = null;\n        }\n        return;\n      }\n\n      // Update the chart data and options\n      this.chart.model.setData(this.data);\n      this.chart.model.setOptions(this.options);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.line-bar-combo-chart-container {\n  position: relative;\n  width: 100%;\n}\n\n.loading-indicator {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  min-height: 200px;\n}\n\n.loading-spinner {\n  border: 4px solid rgba(0, 0, 0, 0.1);\n  border-radius: 50%;\n  border-top: 4px solid #0f62fe;\n  width: 30px;\n  height: 30px;\n  animation: spin 1s linear infinite;\n  margin-bottom: 10px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.no-data-message {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  min-height: 200px;\n  color: #666;\n  font-style: italic;\n}\n\n.chart-container {\n  width: 100%;\n}\n</style>\n"]}]}