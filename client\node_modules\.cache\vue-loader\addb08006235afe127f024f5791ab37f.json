{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\LineBarComboChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\LineBarComboChart.vue", "mtime": 1746725782258}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["LineBarComboChart.vue"], "names": [], "mappings": ";AAcA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "LineBarComboChart.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n  <div class=\"line-bar-combo-chart-container\">\n    <div v-if=\"loading\" class=\"loading-indicator\">\n      <div class=\"loading-spinner\"></div>\n      <span>Loading chart data...</span>\n    </div>\n    <div v-else-if=\"!data || data.length === 0\" class=\"no-data-message\">\n      No data available for the chart\n    </div>\n    <div v-else ref=\"chartContainer\" class=\"chart-container\" :style=\"{ height }\"></div>\n  </div>\n</template>\n\n<script>\nimport { ComboChart } from '@carbon/charts';\nimport '@carbon/charts/styles.css';\n\nexport default {\n  name: 'LineBarComboChart',\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    options: {\n      type: Object,\n      default: () => ({})\n    },\n    height: {\n      type: String,\n      default: '400px'\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      chart: null\n    };\n  },\n  watch: {\n    data: {\n      handler() {\n        this.updateChart();\n      },\n      deep: true\n    },\n    options: {\n      handler() {\n        this.updateChart();\n      },\n      deep: true\n    }\n  },\n  mounted() {\n    this.initChart();\n  },\n  beforeDestroy() {\n    if (this.chart) {\n      this.chart.destroy();\n    }\n  },\n  methods: {\n    initChart() {\n      if (!this.data || this.data.length === 0) return;\n\n      const chartContainer = this.$refs.chartContainer;\n      if (!chartContainer) return;\n\n      // Initialize the chart\n      this.chart = new ComboChart(chartContainer, {\n        data: this.data,\n        options: this.options\n      });\n    },\n    updateChart() {\n      if (!this.chart) {\n        this.initChart();\n        return;\n      }\n\n      if (!this.data || this.data.length === 0) {\n        if (this.chart) {\n          this.chart.destroy();\n          this.chart = null;\n        }\n        return;\n      }\n\n      // Update the chart data and options\n      this.chart.model.setData(this.data);\n      this.chart.model.setOptions(this.options);\n    }\n  }\n};\n</script>\n\n<style scoped>\n.line-bar-combo-chart-container {\n  position: relative;\n  width: 100%;\n}\n\n.loading-indicator {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  min-height: 200px;\n}\n\n.loading-spinner {\n  border: 4px solid rgba(0, 0, 0, 0.1);\n  border-radius: 50%;\n  border-top: 4px solid #0f62fe;\n  width: 30px;\n  height: 30px;\n  animation: spin 1s linear infinite;\n  margin-bottom: 10px;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.no-data-message {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  min-height: 200px;\n  color: #666;\n  font-style: italic;\n}\n\n.chart-container {\n  width: 100%;\n}\n</style>\n"]}]}