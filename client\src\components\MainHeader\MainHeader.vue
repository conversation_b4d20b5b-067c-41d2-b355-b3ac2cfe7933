<template>
  <cv-header aria-label="Carbon header">
    <cv-header-menu-button
      aria-label="Header menu"
      aria-controls="side-nav"
      :active="expandedSideNav"
      @click="$emit('panel-toggle')"
    />
    <cv-skip-to-content href="#main-content">Skip to content</cv-skip-to-content>

    <!-- Make the name clickable by adding a click event -->
    <cv-header-name href="javascript:void(0)" prefix="IBM" @click="navigateTo('/home-page')">STATIT 2.0</cv-header-name>

    <template v-slot:left-panels>
      <cv-side-nav id="side-nav" @panel-resize="onPanelResize" :rail="true" :fixed="useFixed" :expanded="expandedSideNav">
        <cv-side-nav-items>
          <cv-side-nav-menu title="Menu">
            <cv-side-nav-menu-item @click="navigateTo('/commodity-overview')">Commodity Page</cv-side-nav-menu-item>
            <cv-side-nav-menu-item @click="navigateTo('/system-level-fpy')">System Level FPY</cv-side-nav-menu-item>
            <cv-side-nav-menu-item @click="navigateTo('/pn-analysis')">PN Analysis</cv-side-nav-menu-item>
            <cv-side-nav-menu-item @click="navigateTo('/phase1')">Phase 1</cv-side-nav-menu-item>
            <cv-side-nav-menu-item @click="navigateTo('/phase1b')">Phase 1B (AI Query)</cv-side-nav-menu-item>
            <cv-side-nav-menu-item @click="navigateTo('/saved-reports')">Saved Reports</cv-side-nav-menu-item>
            <cv-side-nav-menu-item @click="navigateTo('/validations')">Validations</cv-side-nav-menu-item>
            <cv-side-nav-menu-item @click="navigateTo('/action-tracker')">Action Tracker</cv-side-nav-menu-item>
            <cv-side-nav-menu-item @click="navigateTo('/pqe-dashboard')">PQE Dashboard</cv-side-nav-menu-item>
            <cv-side-nav-menu-item @click="navigateTo('/watsonX')">Watson Assistant</cv-side-nav-menu-item>
            <cv-side-nav-menu-item @click="navigateTo('/XFactorOverall')">X-Factor Overall</cv-side-nav-menu-item>
            <cv-side-nav-menu-item @click="navigateTo('/x-factor-analysis')">Part Group Analysis</cv-side-nav-menu-item>
            <cv-side-nav-menu-item @click="navigateTo('/metis-xfactors')">Metis XFactors</cv-side-nav-menu-item>
          </cv-side-nav-menu>
        </cv-side-nav-items>
      </cv-side-nav>
    </template>
  </cv-header>
</template>

<script>
export default {
  name: 'MainHeader',
  props: {
    expandedSideNav: {
      type: Boolean,
      default: true,
    },
    useFixed: {
      type: Boolean,
      default: false,
    }
  },
  methods: {
    navigateTo(route) {
      this.$router.push(route); // Navigate to the specified route
    },
    onPanelResize() {
      // Handle panel resize event if needed
    }
  }
};
</script>

<style scoped>
/* Add specific styles for the header here, if needed */
</style>
