const configSwitch = require("./config.js");
const config = configSwitch.config();
const ibmdb = require("ibm_db");
const ldap = require("ldapjs");
const path = require('path');
const xls = require('xlsjs');
const SpeechToTextV1 = require('ibm-watson/speech-to-text/v1');
const { IamAuthenticator } = require('ibm-watson/auth');
const AudioRecorder = require('node-audiorecorder'); // New package for microphone input



// Initialize IBM Watson Speech to Text service
const speechToText = new SpeechToTextV1({
  authenticator: new IamAuthenticator({
    apikey: "UvbY37M5AB871lwVLCv66TpJawWQXi-7f3Y6MJl0JIsk" // Replace with your Watson API key
  }),
  serviceUrl: "https://api.us-south.speech-to-text.watson.cloud.ibm.com/instances/7f5402e5-d7b6-41f5-89dd-d145d6c80b00" // Replace with your Watson URL
});

// Each export calls a different function. Pass values from client and the callback
exports.populate_watsonx_line = (req, res) => {
  populate_watsonx_line(req.body, function (err, data) {
    res.status(201).json(data);
  });
};

exports.transcribe = (req, res) => {
  transcribe_from_microphone(function (err, transcript) {
    if (err) {
      res.status(500).json({ error: err.message });
    } else {
      res.status(200).json({ transcript });
    }
  });
};

exports.prompt_watsonx = (req, res) => {
  const axios = require('axios');
  const logger = require('../utils/logger');

  // Extract parameters from request
  const { model_id, prompt, temperature, api_key, project_id } = req.body;

  logger.logInfo('WatsonX.ai API request received', 'prompt_watsonx');
  logger.logInfo(`Parameters: model_id=${model_id}, prompt_length=${prompt ? prompt.length : 0}, temperature=${temperature}, project_id=${project_id}, api_key=${api_key ? 'Provided' : 'Missing'}`, 'prompt_watsonx');

  if (!model_id || !prompt || !api_key || !project_id) {
    logger.logError('Missing required parameters for WatsonX.ai API call', null, 'prompt_watsonx');
    return res.status(400).json({
      status: 'error',
      error: 'Missing required parameters: model_id, prompt, api_key, and project_id are required'
    });
  }

  logger.logInfo(`Sending prompt to WatsonX.ai model: ${model_id}`, 'prompt_watsonx');
  logger.logInfo(`Prompt (first 100 chars): ${prompt.substring(0, 100)}...`, 'prompt_watsonx');
  logger.logInfo(`Temperature: ${temperature || 0.7}`, 'prompt_watsonx');

  // First, get an IAM token using the API key
  const iamUrl = 'https://iam.cloud.ibm.com/identity/token';
  const iamData = new URLSearchParams();
  iamData.append('grant_type', 'urn:ibm:params:oauth:grant-type:apikey');
  iamData.append('apikey', api_key);

  const iamHeaders = {
    'Content-Type': 'application/x-www-form-urlencoded',
    'Accept': 'application/json'
  };

  logger.logInfo('Requesting IAM token from IBM Cloud...', 'prompt_watsonx');

  axios.post(iamUrl, iamData, { headers: iamHeaders })
    .then(iamResponse => {
      logger.logInfo(`Received IAM response: ${iamResponse.status}`, 'prompt_watsonx');

      if (!iamResponse.data || !iamResponse.data.access_token) {
        logger.logError('Failed to get IAM token', iamResponse.data, 'prompt_watsonx');
        return res.status(500).json({
          status: 'error',
          error: 'Failed to authenticate with IBM Cloud'
        });
      }

      const accessToken = iamResponse.data.access_token;
      logger.logInfo(`Successfully obtained IAM token. Token length: ${accessToken.length}`, 'prompt_watsonx');

      // Now call the WatsonX.ai API with the token - using the correct endpoint from IBM Cloud docs
      const watsonxUrl = 'https://us-south.ml.cloud.ibm.com/ml/v1/text/generation?version=2023-05-29';

      // Request headers with the IAM token
      const watsonxHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      };

      // Request body - updated format for the new API
      const watsonxData = {
        model_id: model_id,
        input: prompt,
        parameters: {
          temperature: parseFloat(temperature) || 0.7,
          max_new_tokens: 1024,
          min_new_tokens: 0,
          stop_sequences: [],
          repetition_penalty: 1.0,
          decoding_method: "greedy"
        },
        project_id: project_id
      };

      logger.logInfo('Sending request to WatsonX.ai', 'prompt_watsonx');
      logger.logInfo(`URL: ${watsonxUrl}`, 'prompt_watsonx');
      logger.logInfo(`Model ID: ${watsonxData.model_id}`, 'prompt_watsonx');
      logger.logInfo(`Project ID: ${watsonxData.project_id}`, 'prompt_watsonx');
      logger.logInfo(`Input length: ${watsonxData.input.length}`, 'prompt_watsonx');
      logger.logInfo(`Parameters: ${JSON.stringify(watsonxData.parameters)}`, 'prompt_watsonx');

      // Send request to WatsonX.ai
      return axios.post(watsonxUrl, watsonxData, { headers: watsonxHeaders });
    })
    .then(response => {
      logger.logInfo(`Received response from WatsonX.ai. Status: ${response.status}`, 'prompt_watsonx');

      if (response.data && response.data.results && response.data.results.length > 0) {
        const generatedText = response.data.results[0].generated_text;
        logger.logInfo(`Generated text length: ${generatedText ? generatedText.length : 0}`, 'prompt_watsonx');

        if (generatedText && generatedText.length > 0) {
          logger.logInfo(`Generated text: ${generatedText}`, 'prompt_watsonx');

          res.status(200).json({
            status: 'success',
            generated_text: generatedText
          });
        } else {
          logger.logError('Empty generated text from WatsonX.ai', response.data, 'prompt_watsonx');

          res.status(500).json({
            status: 'error',
            error: 'Empty response from WatsonX.ai'
          });
        }
      } else {
        logger.logError('Unexpected response format from WatsonX.ai', response.data, 'prompt_watsonx');

        res.status(500).json({
          status: 'error',
          error: 'Unexpected response format from WatsonX.ai'
        });
      }
    })
    .catch(error => {
      logger.logError('Error calling WatsonX.ai', error, 'prompt_watsonx');

      let errorDetails = 'Unknown error';

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        errorDetails = `Status: ${error.response.status}, Data: ${JSON.stringify(error.response.data)}`;
      } else if (error.request) {
        // The request was made but no response was received
        errorDetails = 'No response received from server';
      } else {
        // Something happened in setting up the request that triggered an Error
        errorDetails = error.message;
      }

      res.status(500).json({
        status: 'error',
        error: errorDetails
      });
    });
};


function transcribe_from_microphone(callback) {
  let responseObj = {
    transcript: null,
    status_res: null
  };

  const options = {
    program: `sox`, // Use arecord (instead of sox) for microphone input
    device: null, // Recording device to use, e.g. `hw:1,0`

    bits: 16, // Sample size.
    channels: 1, // Channel count.
    encoding: `signed-integer`, // Encoding type.
    format: `S16_LE`, // Encoding type for arecord
    rate: 16000, // Sample rate.
    type: `wav`, // Format type.

    silence: 2, // Duration of silence before it stops recording.
    thresholdStart: 0.5, // Silence threshold to start recording.
    thresholdStop: 0.5, // Silence threshold to stop recording.
    keepSilence: true, // Keep silence in the recording.
  }

  const logger = console;

  // Create an instance of AudioRecorder with the defined options
  let audioRecorder = new AudioRecorder(options, logger);

  // Start recording
  const micInstance = audioRecorder.start();
  // Create a recognize stream for Watson
  const recognizeStream = speechToText.recognizeUsingWebSocket({
    contentType: 'audio/l16; rate=16000; channels=1', // PCM audio format
  });

  console.log("Watson API Config:", speechToText);


  recognizeStream.on('open', () => console.log("Watson WebSocket opened"));
recognizeStream.on('close', () => console.log("Watson WebSocket closed"));
recognizeStream.on('error', err => console.error("Watson WebSocket error:", err));

  // Pipe the audio stream from recorder to Watson
  micInstance.stream().pipe(recognizeStream);

  const micStream = micInstance.stream();
  micStream.on('data', chunk => {
    console.log("Microphone data chunk received:", chunk.length);
  });
  micStream.pipe(recognizeStream);



  recognizeStream.on('data', event => {

    if (event.results && event.results[0] && event.results[0].final) {
      responseObj.transcript = event.results[0].alternatives[0].transcript;
      console.log(2)
      responseObj.status_res = "success";
      micInstance.stop();  // Stop the recording when transcription is done
      callback(null, responseObj);
    }
  });

  recognizeStream.on('error', err => {
    micInstance.stop();  // Stop the recording in case of error
    callback(err, null);
  });

  // Optional: Stop recording after a certain time (e.g., 30 seconds)
  setTimeout(() => {
    micInstance.stop();  // Stop recording after 30 seconds
  }, 30000);
}

function populate_watsonx_line(values, callback) {
  let responseObj = {
    data: [],
    status_res: null
  };

  const filePath = path.join(__dirname, `../excel/Drill_data_test.xls`);
  const workbook = xls.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const sheet = xls.utils.sheet_to_json(workbook.Sheets[sheetName]);

  // Initialize an empty array to hold the row objects
  const rowsList = [];
  sheet.forEach(row => {
    row.group = row["Code Name"];
    row.value = row.XFactor;

    if (row.group === values["comp1"] || row.group === values["comp2"]) {
      if (`${row.Period}-01T04:00:00.000Z` > `${values["startDate"]}T04:00:00.000Z` && `${row.Period}-01T04:00:00.000Z` < `${values["endDate"]}T04:00:00.000Z`) {
        rowsList.push({ date: row.Period, group: row.group, value: row.value });
      }
    }
  });

  responseObj.data = rowsList;
  responseObj.status_res = "success";
  callback(null, responseObj);
}
