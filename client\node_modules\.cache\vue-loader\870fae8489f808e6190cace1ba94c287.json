{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1B\\Phase1B.vue?vue&type=style&index=0&id=4c478242&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1B\\Phase1B.vue", "mtime": 1757086169836}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zaG9ydC1kcm9wZG93biB7CiAgbWF4LXdpZHRoOiAyMDBweDsgLyogQWRqdXN0IHRoZSB3aWR0aCBhcyBuZWVkZWQgKi8KfQoKLnN1Ym1pdC1idXR0b24gewogIG1hcmdpbi10b3A6IDIwcHg7IC8qIEFkZCBzb21lIHNwYWNpbmcgYWJvdmUgdGhlIGJ1dHRvbiAqLwp9CgouZmxleC1jb250YWluZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7IC8qIEFsaWduIGl0ZW1zIGF0IHRoZSB0b3Agb2YgdGhlIGNvbnRhaW5lciAqLwogIGdhcDogMjBweDsgLyogU3BhY2UgYmV0d2VlbiBkcm9wZG93bnMgYW5kIHRoZSBjaGFydCAqLwp9CgoubGVmdC1jb2x1bW4gewogIG1hcmdpbi10b3A6MTVweDsKICBmbGV4OiAxOyAvKiBUYWtlIHVwIDEgcGFydCBvZiB0aGUgc3BhY2UgKi8KICBtYXgtd2lkdGg6IDMwMHB4OyAvKiBPcHRpb25hbDogQ29udHJvbCBtYXggd2lkdGggb2YgdGhlIGxlZnQgY29sdW1uICovCn0KCi5yaWdodC1jb2x1bW4gewogIGZsZXg6IDM7IC8qIFRha2UgdXAgMyBwYXJ0cyBvZiB0aGUgc3BhY2UgKi8KfQoKLmFpLXF1ZXJ5LXNlY3Rpb24gewogIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgcGFkZGluZzogMTVweDsKICAvKiBib3JkZXI6IDFweCBzb2xpZCAjZTBlMGUwOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjRmNGY0OyAqLwp9CgouYWktcXVlcnktbGFiZWwgewogIGZvbnQtd2VpZ2h0OiA2MDA7CiAgbWFyZ2luLWJvdHRvbTogMTBweDsKICBjb2xvcjogI2UzZTNlMzsKfQoKLmFpLXRleHRhcmVhIHsKICB3aWR0aDogMTAwJTsKICBtYXJnaW4tYm90dG9tOiAxNXB4Owp9CgouYWktYnV0dG9ucyB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDEwcHg7Cn0KCi5xdWVyeS1tZXRob2QtdG9nZ2xlIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4Owp9CgoubWFudWFsLWNvbnRyb2xzIHsKICBtYXJnaW4tdG9wOiAxNXB4Owp9Cg=="}, {"version": 3, "sources": ["Phase1B.vue"], "names": [], "mappings": ";AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "Phase1B.vue", "sourceRoot": "src/views/Phase1B", "sourcesContent": ["<style scoped>\n.short-dropdown {\n  max-width: 200px; /* Adjust the width as needed */\n}\n\n.submit-button {\n  margin-top: 20px; /* Add some spacing above the button */\n}\n\n.flex-container {\n  display: flex;\n  align-items: flex-start; /* Align items at the top of the container */\n  gap: 20px; /* Space between dropdowns and the chart */\n}\n\n.left-column {\n  margin-top:15px;\n  flex: 1; /* Take up 1 part of the space */\n  max-width: 300px; /* Optional: Control max width of the left column */\n}\n\n.right-column {\n  flex: 3; /* Take up 3 parts of the space */\n}\n\n.ai-query-section {\n  margin-bottom: 20px;\n  padding: 15px;\n  /* border: 1px solid #e0e0e0;\n  border-radius: 4px;\n  background-color: #f4f4f4; */\n}\n\n.ai-query-label {\n  font-weight: 600;\n  margin-bottom: 10px;\n  color: #e3e3e3;\n}\n\n.ai-textarea {\n  width: 100%;\n  margin-bottom: 15px;\n}\n\n.ai-buttons {\n  display: flex;\n  gap: 10px;\n}\n\n.query-method-toggle {\n  margin-bottom: 20px;\n}\n\n.manual-controls {\n  margin-top: 15px;\n}\n</style>\n\n<template>\n  \n  <cv-grid>\n    <template>\n    <MainHeader :expandedSideNav=false :useFixed=\"useFixed\" />\n    <div class=\"left-column\">\n      <h1>Phase 1</h1>\n\n      <!-- Query Method Toggle -->\n      <div class=\"query-method-toggle\">\n        <cv-radio-group \n          v-model=\"queryMethod\" \n          @change=\"handleQueryMethodChange\"\n          label=\"Query Method\"\n          \n        >\n          <cv-radio-button \n            name=\"queryMethod\" \n            label=\"Natural Language Query\" \n            value=\"ai\"\n          />\n          <cv-radio-button \n            name=\"queryMethod\" \n            label=\"Manual Selection\" \n            value=\"manual\"\n          />\n        </cv-radio-group>\n      </div>\n\n      <!-- AI Query Section -->\n      <div v-if=\"queryMethod === 'ai'\" class=\"ai-query-section\">\n        <div class=\"ai-query-label\">What would you like to view?</div>\n        <cv-text-area\n          v-model=\"aiQuery\"\n          placeholder=\"Example: Show me bar chart for PN ABC123 in area XYZ for category defects from 2024-01-01 to 2024-03-31\"\n          rows=\"4\"\n          class=\"ai-textarea\"\n        />\n        <div class=\"ai-buttons\">\n          <cv-button \n            @click=\"processAiQuery\" \n            :disabled=\"loading || !aiQuery.trim()\"\n            kind=\"primary\"\n          >\n            {{ loading ? 'Processing...' : 'Generate Query' }}\n          </cv-button>\n          <cv-button \n            @click=\"clearAiQuery\" \n            kind=\"secondary\"\n          >\n            Clear\n          </cv-button>\n        </div>\n      </div>\n\n      <!-- Manual Controls Section -->\n      <div v-if=\"queryMethod === 'manual'\" class=\"manual-controls\">\n        <cv-radio-group \n          v-model=\"queryType\" \n          @change=\"handleQueryTypeChange\"\n          label=\"Query Type\"\n          \n        >\n          <cv-radio-button \n            name=\"queryType\" \n            label=\"Fab/Ful DefRate\" \n            value=\"fabful\"\n          />\n          <cv-radio-button \n            name=\"queryType\" \n            label=\"Pareto\" \n            value=\"pareto\"\n          />\n          <cv-radio-button \n            name=\"queryType\" \n            label=\"Compare\" \n            value=\"compare\"\n          />\n        </cv-radio-group>\n        <div v-if=\"queryType === 'fabful'\" >\n              \n\n              <div class=\"chart-controls\">\n                <div class=\"control-group\">\n                  <label class=\"control-label\">View By:</label>\n                  <cv-dropdown\n                    v-model=\"rootCauseViewBy\"\n                    @change=\"handleRootCauseViewByChange\"\n                    class=\"control-dropdown\"\n                  >\n                    <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n                    <cv-dropdown-item value=\"vintage\">Vintage</cv-dropdown-item>\n                    <cv-dropdown-item value=\"sector\">Sector</cv-dropdown-item>\n                    <cv-dropdown-item value=\"supplier\">Supplier</cv-dropdown-item>\n                    <cv-dropdown-item value=\"partNum\">Part Number</cv-dropdown-item>\n                  </cv-dropdown>\n                </div>\n\n                <div class=\"control-group\">\n                  <label class=\"control-label\">Time Range:</label>\n                  <cv-dropdown\n                    v-model=\"rootCauseTimeRange\"\n                    @change=\"handleRootCauseTimeRangeChange\"\n                    class=\"control-dropdown\"\n                  >\n                    <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n                    <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n                  </cv-dropdown>\n                </div>\n\n                <div class=\"control-group\" v-if=\"breakoutGroups.length > 0\">\n                  <label class=\"control-label\">Group:</label>\n                  <cv-dropdown\n                    v-model=\"rootCauseSelectedGroup\"\n                    @change=\"handleRootCauseGroupChange\"\n                    class=\"control-dropdown\"\n                  >\n                    <cv-dropdown-item value=\"all\">All Groups</cv-dropdown-item>\n                    <cv-dropdown-item\n                      v-for=\"group in breakoutGroups\"\n                      :key=\"group.name\"\n                      :value=\"group.name\"\n                    >\n                      {{ group.name }}\n                    </cv-dropdown-item>\n                  </cv-dropdown>\n                </div>\n              </div>\n\n              <div class=\"chart-container\">\n                <div v-if=\"isRootCauseDataLoading\" >\n                          Loading Chart...\n                          <RootCauseChart :data = [] :loading=\"isRootCauseDataLoading\"/>\n                        </div>\n                <RootCauseChart\n                 v-if=\"rootCauseChartData.length > 0 && !isRootCauseDataLoading\"\n                  :data=\"rootCauseChartData\"\n                  :viewBy=\"rootCauseViewBy\"\n                  :timeRange=\"rootCauseTimeRange\"\n                  :selectedGroup=\"rootCauseSelectedGroup\"\n                  :loading=\"isRootCauseDataLoading\"\n                  :title = \"rootCauseTitle\"\n                  @bar-click=\"handleRootCauseBarClick\"\n                />\n\n                <div v-if=\"rootCauseChartData.length == 0 && !isRootCauseDataLoading\" >\n                          No data available\n                </div>\n              </div>\n            \n              \n      </div>\n        <div v-if=\"queryType === 'pareto'\" >\n        \n        \n        <!-- PN Dropdown -->\n        <cv-combo-box class=\"short-dropdown\"\n          v-model=\"selectedPN\"\n          label=\"PN\"\n          :options=\"pnOptions\"\n        ></cv-combo-box>\n\n        <!-- Area Dropdown -->\n        <cv-dropdown class=\"short-dropdown\"\n          v-model=\"selectedArea\"\n          label=\"Area\"\n          :items=\"areaOptions\"\n        ></cv-dropdown>\n\n        <!-- Category Dropdown -->\n        <cv-dropdown class=\"short-dropdown\"\n          v-model=\"selectedCategory\"\n          label=\"Category\"\n          :items=\"categoryOptions\"\n        ></cv-dropdown>\n\n         <!-- Date Select -->\n         <cv-date-picker\n          v-model=\"selectedDateRange\"\n          label=\"Choose Date Range\"\n          kind = 'range'\n          :cal-options = \"calOptions\"\n          placeholder = \"yyyy-mm-dd\"\n        ></cv-date-picker>\n\n        <!-- Chart Type Dropdown -->\n        <cv-dropdown class=\"short-dropdown\"\n          v-model=\"selectedChartType\"\n          label=\"Chart Type\"\n          :items=\"chartTypeOptions\"\n        ></cv-dropdown>\n\n        <!-- Submit Button -->\n        <cv-button @click=\"handleSubmit\" class=\"submit-button\">\n          Submit\n        </cv-button>\n      </div>\n      </div>\n\n    </div>\n\n    <div class=\"right-column\">\n      <cv-tile :light=\"lightTile\">\n        <!-- Show loading skeletons while data is being fetched -->\n        <div v-if=\"loading && displayChartType === 'Bar'\">\n          Loading Bar Chart...\n          <BarChart :data = [] :loading=\"loading\"/>\n        </div>\n        <div v-if=\"loading && displayChartType === 'Line'\" >\n          Loading Line Chart...\n          <LineChart :data = [] :loading=\"loading\"/>\n        </div>\n\n        <!-- Show charts after data is loaded -->\n        <BarChart v-if=\"displayChartType === 'Bar' && bar_chart_data.length > 0\" :data=\"bar_chart_data\" @bar-clicked=\"handleBarClick\" :loading=\"loading\"/>\n        <LineChart v-if=\"displayChartType === 'Line' && line_chart_data.length > 0\" :data=\"line_chart_data\" @point-clicked=\"handlePointClick\" :loading=\"loading\"/>\n      </cv-tile>\n    </div>\n  </template>\n  </cv-grid>\n</template>\n\n\n\n<script>\nimport LineChart from '../../components/LineChart'; // Import the LineChart component\nimport BarChart from '../../components/BarChart'; //import barchart\nimport MainHeader from '../../components/MainHeader';\nimport RootCauseChart from '@/components/RootCauseChart/RootCauseChart';\n\nexport default {\n  name: 'Phase1B',\n  components: {\n    LineChart,\n    BarChart,\n    RootCauseChart,\n    MainHeader\n  },\n  data() {\n    return {\n      queryMethod: 'manual', // Default to AI query method\n      queryType: 'fabful', // Default to Fab/Ful DefRate\n      aiQuery: '',\n      selectedPN: \"\",\n      selectedArea: \"\",\n      selectedStartDate: \"\",\n      selectedEndDate: \"\",\n      selectedDateRange: \"\",\n      selectedCategory: \"\",\n      selectedChartType: \"\",\n      pnOptions: [],\n      areaOptions: [],\n      categoryOptions: [],\n      chartTypeOptions: ['Bar', 'Line'],\n      line_chart_data: [],\n      bar_chart_data: [],\n      displayChartType: \"\",\n      chartData: [], \n      lightTile:true,\n      loading: false,\n      calOptions: {dateFormat: \"Y-m-d\"},\n\n      // Root Cause Chart Data\n      rootCauseChartData: [],\n      rootCauseTitle: '',\n      isRootCauseDataLoading: true,\n\n      // Breakout Groups Data\n      breakoutGroups: [],\n\n\n      // Root Cause Chart Controls\n      rootCauseViewBy: 'rootCause',\n      rootCauseTimeRange: '6month',\n      rootCauseSelectedGroup: 'all',\n    };\n  },\n  mounted() {\n    this.get_PNs();\n  },\n  watch: {\n  selectedPN(newPN) {\n    if (newPN) {\n      this.get_categories();\n    }\n  },\n  selectedArea(newArea) {\n    if (newArea) {\n      this.get_categories2(\"area\");\n      console.log(\"DAN1\")\n    }\n  },\n  selectedCategory(newCategory) {\n    if (newCategory) {\n      this.get_categories2(\"category\");\n    }\n  }\n},\n  methods: {\n    async processAiQuery() {\n      if (!this.aiQuery.trim()) return;\n      \n      this.loading = true;\n      \n      try {\n        const token = this.$store.getters.getToken;\n        \n        // Create a prompt for WatsonX.ai to extract parameters\n        const prompt = `\nYou are a data analysis assistant. Extract the following parameters from the user's natural language query for a manufacturing analysis system:\n\nUser Query: \"${this.aiQuery}\"\n\nPlease extract and return ONLY a JSON object with these fields:\n- pn: Part number without leading zeros (string, empty if not specified). Example: if user says \"02EA657\", return \"02EA657\"\n- area: Area/stage. Must be one of: FULL, FAB, FASM, or TEAR (string, empty if not specified)\n- category: Category/root cause like MECH, ELEC, etc. (string, empty if not specified)\n- startDate: Start date in YYYY-MM-DD format (string, empty if not specified) *for example, May 1 2024 is 2024-05-01\n- endDate: End date in YYYY-MM-DD format (string, empty if not specified) *for example, May 1 2024 is 2024-05-01\n- chartType: Either \"Bar\" or \"Line\" (string, default to \"Bar\" if not specified)\n\nExamples:\n- \"Show me bar chart for PN 02EA657 in area FULL for category MECH from 2024-06-04 to 2024-06-25\"\n  Should return: {\"pn\": \"02EA657\", \"area\": \"FULL\", \"category\": \"MECH\", \"startDate\": \"2024-06-04\", \"endDate\": \"2024-06-25\", \"chartType\": \"Bar\"}\n ***THIS IS JUST AN EXAMPLE, DO NOT USE ANY OF THESE VALUES IN THE RESPONSE***\n\n Return only the JSON object, no other text.\n`;\n\n        // Call WatsonX.ai API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"watsonx_prompt\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({\n            model_id: 'ibm/granite-13b-instruct-v2',\n            prompt: prompt,\n            temperature: 0.3,\n            api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',\n            project_id: 'edd72256-56ff-4595-bf4a-350ab686660c'\n          }),\n        });\n\n        const data = await this.handleResponse(response);\n        \n        if (data && data.status === 'success') {\n          try {\n            // Parse the AI response to extract parameters\n            const aiResponse = data.generated_text.trim();\n            console.log(\"AI Response:\", aiResponse);\n            \n            // Try to extract JSON from the response\n            let extractedParams;\n            try {\n              extractedParams = JSON.parse(aiResponse);\n            } catch (e) {\n              // If direct parsing fails, try to find JSON in the response\n              const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n              if (jsonMatch) {\n                extractedParams = JSON.parse(jsonMatch[0]);\n              } else {\n                throw new Error(\"No valid JSON found in AI response\");\n              }\n            }\n            \n            // Apply the extracted parameters with proper formatting\n            if (extractedParams.pn) {\n              // Ensure part number has proper leading zeros (10 digits total)\n              const pn = extractedParams.pn\n              this.selectedPN = pn;\n            }\n            if (extractedParams.area) this.selectedArea = extractedParams.area;\n            if (extractedParams.category) this.selectedCategory = extractedParams.category;\n            if (extractedParams.chartType) this.selectedChartType = extractedParams.chartType;\n\n            if (extractedParams.startDate && extractedParams.endDate) {\n              // Convert date format if needed (from MM-DD-YYYY to YYYY-MM-DD)\n              let startDate = extractedParams.startDate;\n              let endDate = extractedParams.endDate;\n\n              // Check if dates are in MM-DD-YYYY format and convert to YYYY-MM-DD\n              if (startDate.match(/^\\d{2}-\\d{2}-\\d{4}$/)) {\n                const [month, day, year] = startDate.split('-');\n                startDate = `${year}-${month}-${day}`;\n              }\n              if (endDate.match(/^\\d{2}-\\d{2}-\\d{4}$/)) {\n                const [month, day, year] = endDate.split('-');\n                endDate = `${year}-${month}-${day}`;\n              }\n\n              this.selectedDateRange = {\n                startDate: startDate,\n                endDate: endDate\n              };\n            }\n\n            console.log(\"Applied parameters:\", {\n              PN: this.selectedPN,\n              Area: this.selectedArea,\n              Category: this.selectedCategory,\n              ChartType: this.selectedChartType,\n              DateRange: this.selectedDateRange\n            });\n            console.log(\"This is the AIQ:,\", this.aiQuery);\n            // Automatically execute the query\n            this.handleSubmit();\n            \n            \n          } catch (error) {\n            console.error(\"Error parsing AI response:\", error);\n            alert(\"Sorry, I couldn't understand your query. Please try rephrasing it or use manual selection.\");\n          }\n        }\n      } catch (error) {\n        console.error(\"Error processing AI query:\", error);\n        alert(\"Error processing your query. Please try again.\");\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    clearAiQuery() {\n      this.aiQuery = '';\n    },\n\n    clearResults() {\n      this.bar_chart_data = [];\n      this.line_chart_data = [];\n      this.displayChartType = \"\";\n    },\n\n    transformFullDataToChartFormat(fullData) {\n      // Transform the full data format to chart format\n      if (!fullData || !Array.isArray(fullData)) {\n        return [];\n      }\n\n      return fullData.map(item => ({\n        key: item.YEAR_MONTH,\n        value: item.DEFECT_COUNT,\n        group: \"Defect Count\",\n        // Keep the full data for reference\n        fullData: item\n      }));\n    },\n\n\n        // Root Cause Chart methods\n    async loadRootCauseData() {\n      console.log('Loading root cause chart data for PQE Owner Dashboard');\n      this.isRootCauseDataLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n        this.pqeOwner = \"Albert G\";\n        // Calculate date range based on selected time range\n        const endDate = new Date();\n        const startDate = new Date();\n        const monthsToFetch = this.rootCauseTimeRange === '3month' ? 2 : 5; // 3 or 6 months including current\n        startDate.setMonth(endDate.getMonth() - monthsToFetch);\n\n        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;\n        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;\n\n        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}, Group: ${this.rootCauseSelectedGroup}`);\n\n        // Call the new API endpoint\n        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner,\n            startDate: startDateStr,\n            endDate: endDateStr,\n            selectedGroup: this.rootCauseSelectedGroup === 'all' ? null : this.rootCauseSelectedGroup,\n            viewBy: this.rootCauseViewBy\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Transform the API data to chart format\n          if (this.rootCauseViewBy === \"rootCause\") {\n            this.rootCauseTitle = 'Root Cause Analysis';\n          } else if (this.rootCauseViewBy === \"vintage\") {\n            this.rootCauseTitle = 'Vintage Code Analysis';\n          } else if (this.rootCauseViewBy === \"sector\") {\n            this.rootCauseTitle = 'Sector Analysis';\n          } else if (this.rootCauseViewBy === \"supplier\") {\n            this.rootCauseTitle = 'Supplier Code Analysis';\n          } else if (this.rootCauseViewBy === \"partNum\") {\n            this.rootCauseTitle = 'Part Number Analysis';\n          }\n          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);\n          console.log('Root cause chart data loaded:', data.categoryData);\n          console.log('Chart data length:', this.rootCauseChartData.length);\n          console.log('Sample data point:', this.rootCauseChartData[0]);\n          console.log('Breakout groups:', data.breakoutGroups);\n          console.log('Part numbers:', data.partNumbers);\n\n          console.log('Chart data loaded, watcher should handle update');\n        } else {\n          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);\n          // Fall back to mock data\n          this.rootCauseChartData = this.generateMockRootCauseData();\n          console.log('Using mock data:', this.rootCauseChartData.length);\n        }\n      } catch (error) {\n        console.error('Error loading root cause data:', error);\n        // Fall back to mock data\n        this.rootCauseChartData = this.generateMockRootCauseData();\n      } finally {\n        this.isRootCauseDataLoading = false;\n      }\n    },\n\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // or navigating to the MetisXFactors Group tab with this specific category\n        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\\nFail Rate: ${clickedData.value}%`);\n      }\n    },\n\n    transformRootCauseData(categoryData) {\n      // Transform the API response into chart format\n      const chartData = [];\n\n      // categoryData structure: { \"category\": { \"2024-03\": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }\n      for (const [category, monthData] of Object.entries(categoryData)) {\n        // Clean up category name (trim whitespace)\n        const cleanCategory = category.trim();\n        console.log(\"trim cat\", cleanCategory)\n\n        for (const [month, data] of Object.entries(monthData)) {\n          chartData.push({\n            group: cleanCategory,\n            key: month,\n            value: parseFloat(data.failRate.toFixed(2))\n          });\n        }\n      }\n\n      console.log('Transformed root cause data:', chartData);\n      console.log('Unique groups in data:', [...new Set(chartData.map(d => d.group))]);\n      console.log('Unique keys in data:', [...new Set(chartData.map(d => d.key))]);\n      console.log('Value range:', Math.min(...chartData.map(d => d.value)), 'to', Math.max(...chartData.map(d => d.value)));\n      return chartData;\n    },\n\n    generateMockRootCauseData() {\n      // Use the actual breakout groups for this PQE owner instead of generic categories\n      let categories = [];\n\n      if (this.breakoutGroups && this.breakoutGroups.length > 0) {\n        // Use the actual breakout groups for this PQE owner\n        categories = this.breakoutGroups.map(group => group.name);\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      } else {\n        // Fallback to sample data if breakout groups aren't loaded yet\n        if (this.pqeOwner === 'Albert G.') {\n          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];\n        } else if (this.pqeOwner === 'Sarah L.') {\n          categories = ['Stellar Core', 'Nebula Drive'];\n        } else {\n          categories = ['Sample Group 1', 'Sample Group 2'];\n        }\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      }\n\n      // Generate months based on selected time range\n      const now = new Date();\n      const months = [];\n      const monthsToGenerate = this.rootCauseTimeRange === '3month' ? 3 : 6;\n\n      for (let i = monthsToGenerate - 1; i >= 0; i--) {\n        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        months.push(`${year}-${month}`);\n      }\n\n      const data = [];\n\n      months.forEach(month => {\n        categories.forEach(category => {\n          // Generate realistic fail rate data (0-5% range)\n          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%\n          const variation = (Math.random() - 0.5) * 1; // ±0.5%\n          const failRate = Math.max(0.1, baseRate + variation);\n\n          data.push({\n            group: category,\n            key: month,\n            value: parseFloat(failRate.toFixed(2))\n          });\n        });\n      });\n\n      return data;\n    },\n\n    async load_line_chart() {\n      try {\n\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_fail_count_by_category_db\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({ PN: this.selectedPN, category: this.selectedCategory, area: this.selectedArea }),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          // Use full_data if available (Phase1B format), otherwise use counts_by_period (legacy format)\n          if (data.full_data && data.full_data.length > 0) {\n            console.log(\"Using full data format:\", data.full_data);\n            // Transform full data to chart format\n            this.line_chart_data = this.transformFullDataToChartFormat(data.full_data);\n          } else {\n            console.log(\"Using legacy counts_by_period format:\", data.counts_by_period);\n            this.line_chart_data = data.counts_by_period;\n          }\n          console.log(\"Line chart data:\", this.line_chart_data);\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }finally {\n        this.loading = false;  // Set loading to false once data is loaded\n      }\n    },\n\n    async load_bar_chart() {\n      try {\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_fail_count_by_category_db\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({PN: this.selectedPN, category: this.selectedCategory, area: this.selectedArea, startDate: this.selectedStartDate, endDate: this.selectedEndDate}),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          // Use full_data if available (Phase1B format), otherwise use counts_by_period (legacy format)\n          if (data.full_data && data.full_data.length > 0) {\n            console.log(\"Using full data format for bar chart:\", data.full_data);\n            // Transform full data to chart format\n            const transformedData = this.transformFullDataToChartFormat(data.full_data);\n            this.bar_chart_data = transformedData;\n            this.line_chart_data = transformedData;\n          } else {\n            console.log(\"Using legacy counts_by_period format for bar chart:\", data.counts_by_period);\n            this.bar_chart_data = data.counts_by_period;\n            this.line_chart_data = data.counts_by_period;\n          }\n          console.log(\"Bar chart data:\", this.bar_chart_data);\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }finally {\n        this.loading = false;  // Set loading to false once data is loaded\n      }\n    },\n\n    async get_categories() {\n      try {\n        let user_type = this.$store.getters.getUser_type;\n        let action = \"view\";\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unique_categories_db\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({ \"PN\": this.selectedPN, user_type, action }),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          this.categoryOptions = data.all_categories;\n          this.areaOptions = data.all_stages;\n          console.log(this.areaOptions)\n          console.log(\"Received data:\", data);\n\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }\n    },\n\n    async get_PNs() {\n      try {\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unique_pns\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({}),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          this.pnOptions = data.all_pns;\n\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }\n    },\n\n    async get_categories2(changedVar) {\n      try {\n        let user_type = this.$store.getters.getUser_type;\n        let action = \"view\";\n        let area = null;\n        let category = null;\n\n        if (changedVar === \"area\"){\n          area = this.selectedArea\n        }else if (changedVar === \"category\"){\n          category = this.selectedCategory\n        }\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unique_categories_db2\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({ \"PN\": this.selectedPN, \"area\": area, \"category\": category, user_type, action }),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          if (changedVar === \"area\"){\n            this.categoryOptions = data.new_array;\n          } else if (changedVar === \"category\"){\n            this.areaOptions = data.new_array;\n          }\n\n          console.log(\"Received data:\", data);\n\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }\n    },\n\n    handleRootCauseViewByChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseTimeRangeChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseGroupChange() {\n      console.log(`Root cause group changed to: ${this.rootCauseSelectedGroup}`);\n      console.log('Current chart data length before reload:', this.rootCauseChartData.length);\n      this.loadRootCauseData();\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    },\n\n    handleResponse(response) {\n      if (!response.ok) {\n        if (response.status === 401) {\n          this.session_expired_visible = true;\n        }\n      } else {\n        return response.json();\n      }\n    },\n\n    handleBarClick(data) {\n      // Update the chart data to only show the clicked bar\n      console.log(\"Bar data received:\", data);\n      // this.bar_chart_data = [data];\n    },\n\n    handlePointClick(data) {\n      console.log(\"Point clicked:\", data);\n      // Navigate to a new page or display a message\n      this.$router.push({ name: 'HelloPage' });\n    },\n\n    handleQueryMethodChange(newVal) {\n      // console.log(\"Query method changed:\", newVal);\n      this.queryMethod = newVal;\n      this.clearResults();\n    },\n\n    handleQueryTypeChange(newVal) {\n      // console.log(\"Query method changed:\", newVal);\n      this.queryType = newVal;\n      this.clearResults();\n    },\n\n    handleSubmit() {\n      this.loading = true;\n      this.aiQuery = '';\n      this.bar_chart_data = [];\n      this.line_chart_data = [];\n      console.log('PN:', this.selectedPN);\n      console.log('Area:', this.selectedArea);\n      console.log('Category:', this.selectedCategory);\n      console.log('Chart Type:', this.selectedChartType);\n      console.log(`Date Range: Start: ${this.selectedDate}`, );\n      this.selectedStartDate = this.selectedDateRange.startDate;\n      this.selectedEndDate = this.selectedDateRange.endDate;\n      // Update chart data based on selected chart type\n      if (this.selectedChartType === 'Bar') {\n        //this.chartData = this.bar_chart_data;\n        this.displayChartType = 'Bar';\n        this.load_bar_chart()\n      } else if (this.selectedChartType === 'Line') {\n        // this.chartData = this.line_chart_data;\n        this.displayChartType = 'Line';\n        this.load_line_chart()\n      }\n    },\n  }\n};\n</script>\n"]}]}