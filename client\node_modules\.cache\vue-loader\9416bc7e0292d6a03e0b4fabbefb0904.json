{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1B\\Phase1B.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1B\\Phase1B.vue", "mtime": 1757092121550}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBMaW5lQ2hhcnQgZnJvbSAnLi4vLi4vY29tcG9uZW50cy9MaW5lQ2hhcnQnOyAvLyBJbXBvcnQgdGhlIExpbmVDaGFydCBjb21wb25lbnQKaW1wb3J0IEJhckNoYXJ0IGZyb20gJy4uLy4uL2NvbXBvbmVudHMvQmFyQ2hhcnQnOyAvL2ltcG9ydCBiYXJjaGFydAppbXBvcnQgQ29tYm9DaGFydCBmcm9tICcuLi8uLi9jb21wb25lbnRzL0xpbmVCYXJDb21ib0NoYXJ0JzsgLy9pbXBvcnQgY29tYm8gY2hhcnQKaW1wb3J0IE1haW5IZWFkZXIgZnJvbSAnLi4vLi4vY29tcG9uZW50cy9NYWluSGVhZGVyJzsKaW1wb3J0IFJvb3RDYXVzZUNoYXJ0IGZyb20gJ0AvY29tcG9uZW50cy9Sb290Q2F1c2VDaGFydC9Sb290Q2F1c2VDaGFydCc7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ1BoYXNlMUInLAogIGNvbXBvbmVudHM6IHsKICAgIExpbmVDaGFydCwKICAgIEJhckNoYXJ0LAogICAgQ29tYm9DaGFydCwKICAgIFJvb3RDYXVzZUNoYXJ0LAogICAgTWFpbkhlYWRlcgogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHF1ZXJ5TWV0aG9kOiAnbWFudWFsJywgLy8gRGVmYXVsdCB0byBBSSBxdWVyeSBtZXRob2QKICAgICAgcXVlcnlUeXBlOiAnZmFiZnVsJywgLy8gRGVmYXVsdCB0byBGYWIvRnVsIERlZlJhdGUKICAgICAgYWlRdWVyeTogJycsCiAgICAgIHNlbGVjdGVkUE46ICIiLAogICAgICBzZWxlY3RlZEFyZWE6ICIiLAogICAgICBzZWxlY3RlZFN0YXJ0RGF0ZTogIiIsCiAgICAgIHNlbGVjdGVkRW5kRGF0ZTogIiIsCiAgICAgIHNlbGVjdGVkRGF0ZVJhbmdlOiAiIiwKICAgICAgc2VsZWN0ZWRDYXRlZ29yeTogIiIsCiAgICAgIHNlbGVjdGVkQ2hhcnRUeXBlOiAiIiwKICAgICAgcG5PcHRpb25zOiBbXSwKICAgICAgYXJlYU9wdGlvbnM6IFtdLAogICAgICBjYXRlZ29yeU9wdGlvbnM6IFtdLAogICAgICBjaGFydFR5cGVPcHRpb25zOiBbJ0JhcicsICdMaW5lJ10sCiAgICAgIGxpbmVfY2hhcnRfZGF0YTogW10sCiAgICAgIGJhcl9jaGFydF9kYXRhOiBbXSwKICAgICAgZGlzcGxheUNoYXJ0VHlwZTogIiIsCiAgICAgIGNoYXJ0RGF0YTogW10sIAogICAgICBsaWdodFRpbGU6dHJ1ZSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIGNhbE9wdGlvbnM6IHtkYXRlRm9ybWF0OiAiWS1tLWQifSwKCiAgICAgIC8vIFJvb3QgQ2F1c2UgQ2hhcnQgRGF0YQogICAgICByb290Q2F1c2VDaGFydERhdGE6IFtdLAogICAgICByb290Q2F1c2VUaXRsZTogJycsCiAgICAgIGlzUm9vdENhdXNlRGF0YUxvYWRpbmc6IHRydWUsCgogICAgICAvLyBCcmVha291dCBHcm91cHMgRGF0YQogICAgICBicmVha291dEdyb3VwczogW10sCgoKICAgICAgLy8gUm9vdCBDYXVzZSBDaGFydCBDb250cm9scwogICAgICByb290Q2F1c2VWaWV3Qnk6ICdyb290Q2F1c2UnLAogICAgICByb290Q2F1c2VUaW1lUmFuZ2U6ICc2bW9udGgnLAogICAgICByb290Q2F1c2VTZWxlY3RlZEdyb3VwOiAnYWxsJywKCiAgICAgIC8vIENvbWJvIERlZlJhdGUgRGF0YQogICAgICBjb21ib19jaGFydF9kYXRhOiBbXSwKICAgICAgY29tYm9fdGFibGVfZGF0YTogW10sCiAgICAgIGNvbWJvRGF0ZVJhbmdlOiAiIiwKICAgICAgc2VsZWN0ZWRTdXBwbGllcnM6IFtdLAogICAgICBzZWxlY3RlZFBhcnROdW1iZXJzOiBbXSwKICAgICAgc2VsZWN0ZWRWaW50YWdlczogW10sCiAgICAgIHN1cHBsaWVyT3B0aW9uczogW10sCiAgICAgIHBhcnROdW1iZXJPcHRpb25zOiBbXSwKICAgICAgdmludGFnZU9wdGlvbnM6IFtdLAogICAgICBjb21ib1RhYmxlQ29sdW1uczogWwogICAgICAgIHsga2V5OiAncGFydE51bWJlcicsIGhlYWRlcjogJ1BhcnQgTnVtYmVyJyB9LAogICAgICAgIHsga2V5OiAnbW9udGgnLCBoZWFkZXI6ICdNb250aCcgfSwKICAgICAgICB7IGtleTogJ2RlZmVjdENvdW50JywgaGVhZGVyOiAnRGVmZWN0IENvdW50JyB9LAogICAgICAgIHsga2V5OiAndm9sdW1lJywgaGVhZGVyOiAnVm9sdW1lJyB9LAogICAgICAgIHsga2V5OiAnZmFpbFJhdGUnLCBoZWFkZXI6ICdGYWlsIFJhdGUgKCUpJyB9LAogICAgICAgIHsga2V5OiAndGFyZ2V0UmF0ZScsIGhlYWRlcjogJ1RhcmdldCBSYXRlICglKScgfSwKICAgICAgICB7IGtleTogJ3N0YXR1cycsIGhlYWRlcjogJ1N0YXR1cycgfQogICAgICBdLAoKICAgICAgLy8gQ29tcGFyZSBEYXRhCiAgICAgIGNvbXBhcmVQTjE6ICIiLAogICAgICBjb21wYXJlUE4yOiAiIiwKICAgICAgY29tcGFyZURhdGVSYW5nZTogIiIsCiAgICAgIGNvbXBhcmVfY2hhcnRfZGF0YTogW10sCiAgICAgIGNvbXBhcmlzb25fc3VtbWFyeTogW10sCiAgICB9OwogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0X1BOcygpOwogICAgdGhpcy5sb2FkU3VwcGxpZXJzKCk7CiAgICB0aGlzLmxvYWRWaW50YWdlcygpOwogIH0sCiAgd2F0Y2g6IHsKICBzZWxlY3RlZFBOKG5ld1BOKSB7CiAgICBpZiAobmV3UE4pIHsKICAgICAgdGhpcy5nZXRfY2F0ZWdvcmllcygpOwogICAgfQogIH0sCiAgcG5PcHRpb25zKG5ld09wdGlvbnMpIHsKICAgIC8vIFVwZGF0ZSBwYXJ0IG51bWJlciBvcHRpb25zIGZvciBjb21ibyBmdW5jdGlvbmFsaXR5CiAgICB0aGlzLnBhcnROdW1iZXJPcHRpb25zID0gbmV3T3B0aW9ucy5tYXAocG4gPT4gKHsKICAgICAgbGFiZWw6IHBuLAogICAgICB2YWx1ZTogcG4KICAgIH0pKTsKICB9LAogIHNlbGVjdGVkQXJlYShuZXdBcmVhKSB7CiAgICBpZiAobmV3QXJlYSkgewogICAgICB0aGlzLmdldF9jYXRlZ29yaWVzMigiYXJlYSIpOwogICAgICBjb25zb2xlLmxvZygiREFOMSIpCiAgICB9CiAgfSwKICBzZWxlY3RlZENhdGVnb3J5KG5ld0NhdGVnb3J5KSB7CiAgICBpZiAobmV3Q2F0ZWdvcnkpIHsKICAgICAgdGhpcy5nZXRfY2F0ZWdvcmllczIoImNhdGVnb3J5Iik7CiAgICB9CiAgfQp9LAogIG1ldGhvZHM6IHsKICAgIGFzeW5jIHByb2Nlc3NBaVF1ZXJ5KCkgewogICAgICBpZiAoIXRoaXMuYWlRdWVyeS50cmltKCkpIHJldHVybjsKICAgICAgCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIAogICAgICB0cnkgewogICAgICAgIGNvbnN0IHRva2VuID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5nZXRUb2tlbjsKICAgICAgICAKICAgICAgICAvLyBDcmVhdGUgYSBwcm9tcHQgZm9yIFdhdHNvblguYWkgdG8gZXh0cmFjdCBwYXJhbWV0ZXJzCiAgICAgICAgY29uc3QgcHJvbXB0ID0gYApZb3UgYXJlIGEgZGF0YSBhbmFseXNpcyBhc3Npc3RhbnQuIEV4dHJhY3QgdGhlIGZvbGxvd2luZyBwYXJhbWV0ZXJzIGZyb20gdGhlIHVzZXIncyBuYXR1cmFsIGxhbmd1YWdlIHF1ZXJ5IGZvciBhIG1hbnVmYWN0dXJpbmcgYW5hbHlzaXMgc3lzdGVtOgoKVXNlciBRdWVyeTogIiR7dGhpcy5haVF1ZXJ5fSIKClBsZWFzZSBleHRyYWN0IGFuZCByZXR1cm4gT05MWSBhIEpTT04gb2JqZWN0IHdpdGggdGhlc2UgZmllbGRzOgotIHBuOiBQYXJ0IG51bWJlciB3aXRob3V0IGxlYWRpbmcgemVyb3MgKHN0cmluZywgZW1wdHkgaWYgbm90IHNwZWNpZmllZCkuIEV4YW1wbGU6IGlmIHVzZXIgc2F5cyAiMDJFQTY1NyIsIHJldHVybiAiMDJFQTY1NyIKLSBhcmVhOiBBcmVhL3N0YWdlLiBNdXN0IGJlIG9uZSBvZjogRlVMTCwgRkFCLCBGQVNNLCBvciBURUFSIChzdHJpbmcsIGVtcHR5IGlmIG5vdCBzcGVjaWZpZWQpCi0gY2F0ZWdvcnk6IENhdGVnb3J5L3Jvb3QgY2F1c2UgbGlrZSBNRUNILCBFTEVDLCBldGMuIChzdHJpbmcsIGVtcHR5IGlmIG5vdCBzcGVjaWZpZWQpCi0gc3RhcnREYXRlOiBTdGFydCBkYXRlIGluIFlZWVktTU0tREQgZm9ybWF0IChzdHJpbmcsIGVtcHR5IGlmIG5vdCBzcGVjaWZpZWQpICpmb3IgZXhhbXBsZSwgTWF5IDEgMjAyNCBpcyAyMDI0LTA1LTAxCi0gZW5kRGF0ZTogRW5kIGRhdGUgaW4gWVlZWS1NTS1ERCBmb3JtYXQgKHN0cmluZywgZW1wdHkgaWYgbm90IHNwZWNpZmllZCkgKmZvciBleGFtcGxlLCBNYXkgMSAyMDI0IGlzIDIwMjQtMDUtMDEKLSBjaGFydFR5cGU6IEVpdGhlciAiQmFyIiBvciAiTGluZSIgKHN0cmluZywgZGVmYXVsdCB0byAiQmFyIiBpZiBub3Qgc3BlY2lmaWVkKQoKRXhhbXBsZXM6Ci0gIlNob3cgbWUgYmFyIGNoYXJ0IGZvciBQTiAwMkVBNjU3IGluIGFyZWEgRlVMTCBmb3IgY2F0ZWdvcnkgTUVDSCBmcm9tIDIwMjQtMDYtMDQgdG8gMjAyNC0wNi0yNSIKICBTaG91bGQgcmV0dXJuOiB7InBuIjogIjAyRUE2NTciLCAiYXJlYSI6ICJGVUxMIiwgImNhdGVnb3J5IjogIk1FQ0giLCAic3RhcnREYXRlIjogIjIwMjQtMDYtMDQiLCAiZW5kRGF0ZSI6ICIyMDI0LTA2LTI1IiwgImNoYXJ0VHlwZSI6ICJCYXIifQogKioqVEhJUyBJUyBKVVNUIEFOIEVYQU1QTEUsIERPIE5PVCBVU0UgQU5ZIE9GIFRIRVNFIFZBTFVFUyBJTiBUSEUgUkVTUE9OU0UqKioKCiBSZXR1cm4gb25seSB0aGUgSlNPTiBvYmplY3QsIG5vIG90aGVyIHRleHQuCmA7CgogICAgICAgIC8vIENhbGwgV2F0c29uWC5haSBBUEkKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHByb2Nlc3MuZW52LlZVRV9BUFBfQVBJX1BBVEggKyAid2F0c29ueF9wcm9tcHQiLCB7CiAgICAgICAgICBtZXRob2Q6ICJQT1NUIiwKICAgICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgICAgIkNvbnRlbnQtVHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uIiwKICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgdG9rZW4sCiAgICAgICAgICB9LAogICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoewogICAgICAgICAgICBtb2RlbF9pZDogJ2libS9ncmFuaXRlLTEzYi1pbnN0cnVjdC12MicsCiAgICAgICAgICAgIHByb21wdDogcHJvbXB0LAogICAgICAgICAgICB0ZW1wZXJhdHVyZTogMC4zLAogICAgICAgICAgICBhcGlfa2V5OiAnQS05VVplNDhOMTU2by1sTHV4dmpSNUllUFhEaEt3clZIdTYxVWNpUTlhZmwnLAogICAgICAgICAgICBwcm9qZWN0X2lkOiAnZWRkNzIyNTYtNTZmZi00NTk1LWJmNGEtMzUwYWI2ODY2NjBjJwogICAgICAgICAgfSksCiAgICAgICAgfSk7CgogICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCB0aGlzLmhhbmRsZVJlc3BvbnNlKHJlc3BvbnNlKTsKICAgICAgICAKICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLnN0YXR1cyA9PT0gJ3N1Y2Nlc3MnKSB7CiAgICAgICAgICB0cnkgewogICAgICAgICAgICAvLyBQYXJzZSB0aGUgQUkgcmVzcG9uc2UgdG8gZXh0cmFjdCBwYXJhbWV0ZXJzCiAgICAgICAgICAgIGNvbnN0IGFpUmVzcG9uc2UgPSBkYXRhLmdlbmVyYXRlZF90ZXh0LnRyaW0oKTsKICAgICAgICAgICAgY29uc29sZS5sb2coIkFJIFJlc3BvbnNlOiIsIGFpUmVzcG9uc2UpOwogICAgICAgICAgICAKICAgICAgICAgICAgLy8gVHJ5IHRvIGV4dHJhY3QgSlNPTiBmcm9tIHRoZSByZXNwb25zZQogICAgICAgICAgICBsZXQgZXh0cmFjdGVkUGFyYW1zOwogICAgICAgICAgICB0cnkgewogICAgICAgICAgICAgIGV4dHJhY3RlZFBhcmFtcyA9IEpTT04ucGFyc2UoYWlSZXNwb25zZSk7CiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgICAgICAvLyBJZiBkaXJlY3QgcGFyc2luZyBmYWlscywgdHJ5IHRvIGZpbmQgSlNPTiBpbiB0aGUgcmVzcG9uc2UKICAgICAgICAgICAgICBjb25zdCBqc29uTWF0Y2ggPSBhaVJlc3BvbnNlLm1hdGNoKC9ce1tcc1xTXSpcfS8pOwogICAgICAgICAgICAgIGlmIChqc29uTWF0Y2gpIHsKICAgICAgICAgICAgICAgIGV4dHJhY3RlZFBhcmFtcyA9IEpTT04ucGFyc2UoanNvbk1hdGNoWzBdKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCJObyB2YWxpZCBKU09OIGZvdW5kIGluIEFJIHJlc3BvbnNlIik7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIAogICAgICAgICAgICAvLyBBcHBseSB0aGUgZXh0cmFjdGVkIHBhcmFtZXRlcnMgd2l0aCBwcm9wZXIgZm9ybWF0dGluZwogICAgICAgICAgICBpZiAoZXh0cmFjdGVkUGFyYW1zLnBuKSB7CiAgICAgICAgICAgICAgLy8gRW5zdXJlIHBhcnQgbnVtYmVyIGhhcyBwcm9wZXIgbGVhZGluZyB6ZXJvcyAoMTAgZGlnaXRzIHRvdGFsKQogICAgICAgICAgICAgIGNvbnN0IHBuID0gZXh0cmFjdGVkUGFyYW1zLnBuCiAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZFBOID0gcG47CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKGV4dHJhY3RlZFBhcmFtcy5hcmVhKSB0aGlzLnNlbGVjdGVkQXJlYSA9IGV4dHJhY3RlZFBhcmFtcy5hcmVhOwogICAgICAgICAgICBpZiAoZXh0cmFjdGVkUGFyYW1zLmNhdGVnb3J5KSB0aGlzLnNlbGVjdGVkQ2F0ZWdvcnkgPSBleHRyYWN0ZWRQYXJhbXMuY2F0ZWdvcnk7CiAgICAgICAgICAgIGlmIChleHRyYWN0ZWRQYXJhbXMuY2hhcnRUeXBlKSB0aGlzLnNlbGVjdGVkQ2hhcnRUeXBlID0gZXh0cmFjdGVkUGFyYW1zLmNoYXJ0VHlwZTsKCiAgICAgICAgICAgIGlmIChleHRyYWN0ZWRQYXJhbXMuc3RhcnREYXRlICYmIGV4dHJhY3RlZFBhcmFtcy5lbmREYXRlKSB7CiAgICAgICAgICAgICAgLy8gQ29udmVydCBkYXRlIGZvcm1hdCBpZiBuZWVkZWQgKGZyb20gTU0tREQtWVlZWSB0byBZWVlZLU1NLUREKQogICAgICAgICAgICAgIGxldCBzdGFydERhdGUgPSBleHRyYWN0ZWRQYXJhbXMuc3RhcnREYXRlOwogICAgICAgICAgICAgIGxldCBlbmREYXRlID0gZXh0cmFjdGVkUGFyYW1zLmVuZERhdGU7CgogICAgICAgICAgICAgIC8vIENoZWNrIGlmIGRhdGVzIGFyZSBpbiBNTS1ERC1ZWVlZIGZvcm1hdCBhbmQgY29udmVydCB0byBZWVlZLU1NLURECiAgICAgICAgICAgICAgaWYgKHN0YXJ0RGF0ZS5tYXRjaCgvXlxkezJ9LVxkezJ9LVxkezR9JC8pKSB7CiAgICAgICAgICAgICAgICBjb25zdCBbbW9udGgsIGRheSwgeWVhcl0gPSBzdGFydERhdGUuc3BsaXQoJy0nKTsKICAgICAgICAgICAgICAgIHN0YXJ0RGF0ZSA9IGAke3llYXJ9LSR7bW9udGh9LSR7ZGF5fWA7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGlmIChlbmREYXRlLm1hdGNoKC9eXGR7Mn0tXGR7Mn0tXGR7NH0kLykpIHsKICAgICAgICAgICAgICAgIGNvbnN0IFttb250aCwgZGF5LCB5ZWFyXSA9IGVuZERhdGUuc3BsaXQoJy0nKTsKICAgICAgICAgICAgICAgIGVuZERhdGUgPSBgJHt5ZWFyfS0ke21vbnRofS0ke2RheX1gOwogICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZERhdGVSYW5nZSA9IHsKICAgICAgICAgICAgICAgIHN0YXJ0RGF0ZTogc3RhcnREYXRlLAogICAgICAgICAgICAgICAgZW5kRGF0ZTogZW5kRGF0ZQogICAgICAgICAgICAgIH07CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIGNvbnNvbGUubG9nKCJBcHBsaWVkIHBhcmFtZXRlcnM6IiwgewogICAgICAgICAgICAgIFBOOiB0aGlzLnNlbGVjdGVkUE4sCiAgICAgICAgICAgICAgQXJlYTogdGhpcy5zZWxlY3RlZEFyZWEsCiAgICAgICAgICAgICAgQ2F0ZWdvcnk6IHRoaXMuc2VsZWN0ZWRDYXRlZ29yeSwKICAgICAgICAgICAgICBDaGFydFR5cGU6IHRoaXMuc2VsZWN0ZWRDaGFydFR5cGUsCiAgICAgICAgICAgICAgRGF0ZVJhbmdlOiB0aGlzLnNlbGVjdGVkRGF0ZVJhbmdlCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBjb25zb2xlLmxvZygiVGhpcyBpcyB0aGUgQUlROiwiLCB0aGlzLmFpUXVlcnkpOwogICAgICAgICAgICAvLyBBdXRvbWF0aWNhbGx5IGV4ZWN1dGUgdGhlIHF1ZXJ5CiAgICAgICAgICAgIHRoaXMuaGFuZGxlU3VibWl0KCk7CiAgICAgICAgICAgIAogICAgICAgICAgICAKICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIkVycm9yIHBhcnNpbmcgQUkgcmVzcG9uc2U6IiwgZXJyb3IpOwogICAgICAgICAgICBhbGVydCgiU29ycnksIEkgY291bGRuJ3QgdW5kZXJzdGFuZCB5b3VyIHF1ZXJ5LiBQbGVhc2UgdHJ5IHJlcGhyYXNpbmcgaXQgb3IgdXNlIG1hbnVhbCBzZWxlY3Rpb24uIik7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoIkVycm9yIHByb2Nlc3NpbmcgQUkgcXVlcnk6IiwgZXJyb3IpOwogICAgICAgIGFsZXJ0KCJFcnJvciBwcm9jZXNzaW5nIHlvdXIgcXVlcnkuIFBsZWFzZSB0cnkgYWdhaW4uIik7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICAKICAgIGNsZWFyQWlRdWVyeSgpIHsKICAgICAgdGhpcy5haVF1ZXJ5ID0gJyc7CiAgICB9LAoKICAgIGNsZWFyUmVzdWx0cygpIHsKICAgICAgdGhpcy5iYXJfY2hhcnRfZGF0YSA9IFtdOwogICAgICB0aGlzLmxpbmVfY2hhcnRfZGF0YSA9IFtdOwogICAgICB0aGlzLmRpc3BsYXlDaGFydFR5cGUgPSAiIjsKICAgIH0sCgogICAgdHJhbnNmb3JtRnVsbERhdGFUb0NoYXJ0Rm9ybWF0KGZ1bGxEYXRhKSB7CiAgICAgIC8vIFRyYW5zZm9ybSB0aGUgZnVsbCBkYXRhIGZvcm1hdCB0byBjaGFydCBmb3JtYXQKICAgICAgaWYgKCFmdWxsRGF0YSB8fCAhQXJyYXkuaXNBcnJheShmdWxsRGF0YSkpIHsKICAgICAgICByZXR1cm4gW107CiAgICAgIH0KCiAgICAgIHJldHVybiBmdWxsRGF0YS5tYXAoaXRlbSA9PiAoewogICAgICAgIGtleTogaXRlbS5ZRUFSX01PTlRILAogICAgICAgIHZhbHVlOiBpdGVtLkRFRkVDVF9DT1VOVCwKICAgICAgICBncm91cDogIkRlZmVjdCBDb3VudCIsCiAgICAgICAgLy8gS2VlcCB0aGUgZnVsbCBkYXRhIGZvciByZWZlcmVuY2UKICAgICAgICBmdWxsRGF0YTogaXRlbQogICAgICB9KSk7CiAgICB9LAoKCiAgICAgICAgLy8gUm9vdCBDYXVzZSBDaGFydCBtZXRob2RzCiAgICBhc3luYyBsb2FkUm9vdENhdXNlRGF0YSgpIHsKICAgICAgY29uc29sZS5sb2coJ0xvYWRpbmcgcm9vdCBjYXVzZSBjaGFydCBkYXRhIGZvciBQUUUgT3duZXIgRGFzaGJvYXJkJyk7CiAgICAgIHRoaXMuaXNSb290Q2F1c2VEYXRhTG9hZGluZyA9IHRydWU7CgogICAgICB0cnkgewogICAgICAgIC8vIEdldCBhdXRoZW50aWNhdGlvbiBjb25maWcKICAgICAgICBjb25zdCBjb25maWcgPSB0aGlzLmdldEF1dGhDb25maWcoKTsKICAgICAgICB0aGlzLnBxZU93bmVyID0gIkFsYmVydCBHIjsKICAgICAgICAvLyBDYWxjdWxhdGUgZGF0ZSByYW5nZSBiYXNlZCBvbiBzZWxlY3RlZCB0aW1lIHJhbmdlCiAgICAgICAgY29uc3QgZW5kRGF0ZSA9IG5ldyBEYXRlKCk7CiAgICAgICAgY29uc3Qgc3RhcnREYXRlID0gbmV3IERhdGUoKTsKICAgICAgICBjb25zdCBtb250aHNUb0ZldGNoID0gdGhpcy5yb290Q2F1c2VUaW1lUmFuZ2UgPT09ICczbW9udGgnID8gMiA6IDU7IC8vIDMgb3IgNiBtb250aHMgaW5jbHVkaW5nIGN1cnJlbnQKICAgICAgICBzdGFydERhdGUuc2V0TW9udGgoZW5kRGF0ZS5nZXRNb250aCgpIC0gbW9udGhzVG9GZXRjaCk7CgogICAgICAgIGNvbnN0IHN0YXJ0RGF0ZVN0ciA9IGAke3N0YXJ0RGF0ZS5nZXRGdWxsWWVhcigpfS0ke1N0cmluZyhzdGFydERhdGUuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyl9YDsKICAgICAgICBjb25zdCBlbmREYXRlU3RyID0gYCR7ZW5kRGF0ZS5nZXRGdWxsWWVhcigpfS0ke1N0cmluZyhlbmREYXRlLmdldE1vbnRoKCkgKyAxKS5wYWRTdGFydCgyLCAnMCcpfWA7CgogICAgICAgIGNvbnNvbGUubG9nKGBGZXRjaGluZyByb290IGNhdXNlIGRhdGEgZm9yICR7dGhpcy5wcWVPd25lcn0gZnJvbSAke3N0YXJ0RGF0ZVN0cn0gdG8gJHtlbmREYXRlU3RyfSwgR3JvdXA6ICR7dGhpcy5yb290Q2F1c2VTZWxlY3RlZEdyb3VwfWApOwoKICAgICAgICAvLyBDYWxsIHRoZSBuZXcgQVBJIGVuZHBvaW50CiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS1zdGF0aXQyL2dldF9wcWVfcm9vdF9jYXVzZV9hbmFseXNpcycsIHsKICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLAogICAgICAgICAgaGVhZGVyczogewogICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLAogICAgICAgICAgICAuLi5jb25maWcuaGVhZGVycwogICAgICAgICAgfSwKICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsKICAgICAgICAgICAgcHFlT3duZXI6IHRoaXMucHFlT3duZXIsCiAgICAgICAgICAgIHN0YXJ0RGF0ZTogc3RhcnREYXRlU3RyLAogICAgICAgICAgICBlbmREYXRlOiBlbmREYXRlU3RyLAogICAgICAgICAgICBzZWxlY3RlZEdyb3VwOiB0aGlzLnJvb3RDYXVzZVNlbGVjdGVkR3JvdXAgPT09ICdhbGwnID8gbnVsbCA6IHRoaXMucm9vdENhdXNlU2VsZWN0ZWRHcm91cCwKICAgICAgICAgICAgdmlld0J5OiB0aGlzLnJvb3RDYXVzZVZpZXdCeQogICAgICAgICAgfSkKICAgICAgICB9KTsKCiAgICAgICAgaWYgKCFyZXNwb25zZS5vaykgewogICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggcm9vdCBjYXVzZSBkYXRhOiAke3Jlc3BvbnNlLnN0YXR1c30gJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApOwogICAgICAgIH0KCiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTsKCiAgICAgICAgaWYgKGRhdGEuc3RhdHVzX3JlcyA9PT0gJ3N1Y2Nlc3MnKSB7CiAgICAgICAgICAvLyBUcmFuc2Zvcm0gdGhlIEFQSSBkYXRhIHRvIGNoYXJ0IGZvcm1hdAogICAgICAgICAgaWYgKHRoaXMucm9vdENhdXNlVmlld0J5ID09PSAicm9vdENhdXNlIikgewogICAgICAgICAgICB0aGlzLnJvb3RDYXVzZVRpdGxlID0gJ1Jvb3QgQ2F1c2UgQW5hbHlzaXMnOwogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLnJvb3RDYXVzZVZpZXdCeSA9PT0gInZpbnRhZ2UiKSB7CiAgICAgICAgICAgIHRoaXMucm9vdENhdXNlVGl0bGUgPSAnVmludGFnZSBDb2RlIEFuYWx5c2lzJzsKICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5yb290Q2F1c2VWaWV3QnkgPT09ICJzZWN0b3IiKSB7CiAgICAgICAgICAgIHRoaXMucm9vdENhdXNlVGl0bGUgPSAnU2VjdG9yIEFuYWx5c2lzJzsKICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5yb290Q2F1c2VWaWV3QnkgPT09ICJzdXBwbGllciIpIHsKICAgICAgICAgICAgdGhpcy5yb290Q2F1c2VUaXRsZSA9ICdTdXBwbGllciBDb2RlIEFuYWx5c2lzJzsKICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5yb290Q2F1c2VWaWV3QnkgPT09ICJwYXJ0TnVtIikgewogICAgICAgICAgICB0aGlzLnJvb3RDYXVzZVRpdGxlID0gJ1BhcnQgTnVtYmVyIEFuYWx5c2lzJzsKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMucm9vdENhdXNlQ2hhcnREYXRhID0gdGhpcy50cmFuc2Zvcm1Sb290Q2F1c2VEYXRhKGRhdGEuY2F0ZWdvcnlEYXRhKTsKICAgICAgICAgIGNvbnNvbGUubG9nKCdSb290IGNhdXNlIGNoYXJ0IGRhdGEgbG9hZGVkOicsIGRhdGEuY2F0ZWdvcnlEYXRhKTsKICAgICAgICAgIGNvbnNvbGUubG9nKCdDaGFydCBkYXRhIGxlbmd0aDonLCB0aGlzLnJvb3RDYXVzZUNoYXJ0RGF0YS5sZW5ndGgpOwogICAgICAgICAgY29uc29sZS5sb2coJ1NhbXBsZSBkYXRhIHBvaW50OicsIHRoaXMucm9vdENhdXNlQ2hhcnREYXRhWzBdKTsKICAgICAgICAgIGNvbnNvbGUubG9nKCdCcmVha291dCBncm91cHM6JywgZGF0YS5icmVha291dEdyb3Vwcyk7CiAgICAgICAgICBjb25zb2xlLmxvZygnUGFydCBudW1iZXJzOicsIGRhdGEucGFydE51bWJlcnMpOwoKICAgICAgICAgIGNvbnNvbGUubG9nKCdDaGFydCBkYXRhIGxvYWRlZCwgd2F0Y2hlciBzaG91bGQgaGFuZGxlIHVwZGF0ZScpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCByb290IGNhdXNlIGRhdGE6JywgZGF0YS5zcWxfZXJyb3JfbXNnIHx8IGRhdGEubWVzc2FnZSk7CiAgICAgICAgICAvLyBGYWxsIGJhY2sgdG8gbW9jayBkYXRhCiAgICAgICAgICB0aGlzLnJvb3RDYXVzZUNoYXJ0RGF0YSA9IHRoaXMuZ2VuZXJhdGVNb2NrUm9vdENhdXNlRGF0YSgpOwogICAgICAgICAgY29uc29sZS5sb2coJ1VzaW5nIG1vY2sgZGF0YTonLCB0aGlzLnJvb3RDYXVzZUNoYXJ0RGF0YS5sZW5ndGgpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIHJvb3QgY2F1c2UgZGF0YTonLCBlcnJvcik7CiAgICAgICAgLy8gRmFsbCBiYWNrIHRvIG1vY2sgZGF0YQogICAgICAgIHRoaXMucm9vdENhdXNlQ2hhcnREYXRhID0gdGhpcy5nZW5lcmF0ZU1vY2tSb290Q2F1c2VEYXRhKCk7CiAgICAgIH0gZmluYWxseSB7CiAgICAgICAgdGhpcy5pc1Jvb3RDYXVzZURhdGFMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCgogICAgaGFuZGxlUm9vdENhdXNlQmFyQ2xpY2soZXZlbnQpIHsKICAgICAgaWYgKGV2ZW50ICYmIGV2ZW50LmRhdGEpIHsKICAgICAgICBjb25zdCBjbGlja2VkRGF0YSA9IGV2ZW50LmRhdGE7CiAgICAgICAgY29uc29sZS5sb2coYFJvb3QgY2F1c2UgYmFyIGNsaWNrZWQgZm9yIGNhdGVnb3J5OiAke2NsaWNrZWREYXRhLmdyb3VwfSwgbW9udGg6ICR7Y2xpY2tlZERhdGEua2V5fWApOwoKICAgICAgICAvLyBZb3UgY2FuIGltcGxlbWVudCBhZGRpdGlvbmFsIGZ1bmN0aW9uYWxpdHkgaGVyZSwgc3VjaCBhcyBzaG93aW5nIG1vcmUgZGV0YWlscwogICAgICAgIC8vIG9yIG5hdmlnYXRpbmcgdG8gdGhlIE1ldGlzWEZhY3RvcnMgR3JvdXAgdGFiIHdpdGggdGhpcyBzcGVjaWZpYyBjYXRlZ29yeQogICAgICAgIGFsZXJ0KGBDbGlja2VkIG9uICR7Y2xpY2tlZERhdGEuZ3JvdXB9IGZvciAke2NsaWNrZWREYXRhLmtleX1cbkZhaWwgUmF0ZTogJHtjbGlja2VkRGF0YS52YWx1ZX0lYCk7CiAgICAgIH0KICAgIH0sCgogICAgdHJhbnNmb3JtUm9vdENhdXNlRGF0YShjYXRlZ29yeURhdGEpIHsKICAgICAgLy8gVHJhbnNmb3JtIHRoZSBBUEkgcmVzcG9uc2UgaW50byBjaGFydCBmb3JtYXQKICAgICAgY29uc3QgY2hhcnREYXRhID0gW107CgogICAgICAvLyBjYXRlZ29yeURhdGEgc3RydWN0dXJlOiB7ICJjYXRlZ29yeSI6IHsgIjIwMjQtMDMiOiB7IGRlZmVjdHM6IDUsIHZvbHVtZTogMTAwMCwgZmFpbFJhdGU6IDAuNSB9LCAuLi4gfSwgLi4uIH0KICAgICAgZm9yIChjb25zdCBbY2F0ZWdvcnksIG1vbnRoRGF0YV0gb2YgT2JqZWN0LmVudHJpZXMoY2F0ZWdvcnlEYXRhKSkgewogICAgICAgIC8vIENsZWFuIHVwIGNhdGVnb3J5IG5hbWUgKHRyaW0gd2hpdGVzcGFjZSkKICAgICAgICBjb25zdCBjbGVhbkNhdGVnb3J5ID0gY2F0ZWdvcnkudHJpbSgpOwogICAgICAgIGNvbnNvbGUubG9nKCJ0cmltIGNhdCIsIGNsZWFuQ2F0ZWdvcnkpCgogICAgICAgIGZvciAoY29uc3QgW21vbnRoLCBkYXRhXSBvZiBPYmplY3QuZW50cmllcyhtb250aERhdGEpKSB7CiAgICAgICAgICBjaGFydERhdGEucHVzaCh7CiAgICAgICAgICAgIGdyb3VwOiBjbGVhbkNhdGVnb3J5LAogICAgICAgICAgICBrZXk6IG1vbnRoLAogICAgICAgICAgICB2YWx1ZTogcGFyc2VGbG9hdChkYXRhLmZhaWxSYXRlLnRvRml4ZWQoMikpCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIGNvbnNvbGUubG9nKCdUcmFuc2Zvcm1lZCByb290IGNhdXNlIGRhdGE6JywgY2hhcnREYXRhKTsKICAgICAgY29uc29sZS5sb2coJ1VuaXF1ZSBncm91cHMgaW4gZGF0YTonLCBbLi4ubmV3IFNldChjaGFydERhdGEubWFwKGQgPT4gZC5ncm91cCkpXSk7CiAgICAgIGNvbnNvbGUubG9nKCdVbmlxdWUga2V5cyBpbiBkYXRhOicsIFsuLi5uZXcgU2V0KGNoYXJ0RGF0YS5tYXAoZCA9PiBkLmtleSkpXSk7CiAgICAgIGNvbnNvbGUubG9nKCdWYWx1ZSByYW5nZTonLCBNYXRoLm1pbiguLi5jaGFydERhdGEubWFwKGQgPT4gZC52YWx1ZSkpLCAndG8nLCBNYXRoLm1heCguLi5jaGFydERhdGEubWFwKGQgPT4gZC52YWx1ZSkpKTsKICAgICAgcmV0dXJuIGNoYXJ0RGF0YTsKICAgIH0sCgogICAgZ2VuZXJhdGVNb2NrUm9vdENhdXNlRGF0YSgpIHsKICAgICAgLy8gVXNlIHRoZSBhY3R1YWwgYnJlYWtvdXQgZ3JvdXBzIGZvciB0aGlzIFBRRSBvd25lciBpbnN0ZWFkIG9mIGdlbmVyaWMgY2F0ZWdvcmllcwogICAgICBsZXQgY2F0ZWdvcmllcyA9IFtdOwoKICAgICAgaWYgKHRoaXMuYnJlYWtvdXRHcm91cHMgJiYgdGhpcy5icmVha291dEdyb3Vwcy5sZW5ndGggPiAwKSB7CiAgICAgICAgLy8gVXNlIHRoZSBhY3R1YWwgYnJlYWtvdXQgZ3JvdXBzIGZvciB0aGlzIFBRRSBvd25lcgogICAgICAgIGNhdGVnb3JpZXMgPSB0aGlzLmJyZWFrb3V0R3JvdXBzLm1hcChncm91cCA9PiBncm91cC5uYW1lKTsKCiAgICAgICAgLy8gSWYgYSBzcGVjaWZpYyBncm91cCBpcyBzZWxlY3RlZCwgZmlsdGVyIHRvIG9ubHkgdGhhdCBncm91cAogICAgICAgIGlmICh0aGlzLnJvb3RDYXVzZVNlbGVjdGVkR3JvdXAgIT09ICdhbGwnKSB7CiAgICAgICAgICBjYXRlZ29yaWVzID0gY2F0ZWdvcmllcy5maWx0ZXIoY2F0ZWdvcnkgPT4gY2F0ZWdvcnkgPT09IHRoaXMucm9vdENhdXNlU2VsZWN0ZWRHcm91cCk7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIC8vIEZhbGxiYWNrIHRvIHNhbXBsZSBkYXRhIGlmIGJyZWFrb3V0IGdyb3VwcyBhcmVuJ3QgbG9hZGVkIHlldAogICAgICAgIGlmICh0aGlzLnBxZU93bmVyID09PSAnQWxiZXJ0IEcuJykgewogICAgICAgICAgY2F0ZWdvcmllcyA9IFsnRmFuIFRoZW1pcycsICdWaWN0b3JpYSBDcnlwdG8nLCAnUXVhbnR1bSBOZXh1cyddOwogICAgICAgIH0gZWxzZSBpZiAodGhpcy5wcWVPd25lciA9PT0gJ1NhcmFoIEwuJykgewogICAgICAgICAgY2F0ZWdvcmllcyA9IFsnU3RlbGxhciBDb3JlJywgJ05lYnVsYSBEcml2ZSddOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjYXRlZ29yaWVzID0gWydTYW1wbGUgR3JvdXAgMScsICdTYW1wbGUgR3JvdXAgMiddOwogICAgICAgIH0KCiAgICAgICAgLy8gSWYgYSBzcGVjaWZpYyBncm91cCBpcyBzZWxlY3RlZCwgZmlsdGVyIHRvIG9ubHkgdGhhdCBncm91cAogICAgICAgIGlmICh0aGlzLnJvb3RDYXVzZVNlbGVjdGVkR3JvdXAgIT09ICdhbGwnKSB7CiAgICAgICAgICBjYXRlZ29yaWVzID0gY2F0ZWdvcmllcy5maWx0ZXIoY2F0ZWdvcnkgPT4gY2F0ZWdvcnkgPT09IHRoaXMucm9vdENhdXNlU2VsZWN0ZWRHcm91cCk7CiAgICAgICAgfQogICAgICB9CgogICAgICAvLyBHZW5lcmF0ZSBtb250aHMgYmFzZWQgb24gc2VsZWN0ZWQgdGltZSByYW5nZQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICBjb25zdCBtb250aHMgPSBbXTsKICAgICAgY29uc3QgbW9udGhzVG9HZW5lcmF0ZSA9IHRoaXMucm9vdENhdXNlVGltZVJhbmdlID09PSAnM21vbnRoJyA/IDMgOiA2OwoKICAgICAgZm9yIChsZXQgaSA9IG1vbnRoc1RvR2VuZXJhdGUgLSAxOyBpID49IDA7IGktLSkgewogICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShub3cuZ2V0RnVsbFllYXIoKSwgbm93LmdldE1vbnRoKCkgLSBpLCAxKTsKICAgICAgICBjb25zdCB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpOwogICAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKGRhdGUuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyk7CiAgICAgICAgbW9udGhzLnB1c2goYCR7eWVhcn0tJHttb250aH1gKTsKICAgICAgfQoKICAgICAgY29uc3QgZGF0YSA9IFtdOwoKICAgICAgbW9udGhzLmZvckVhY2gobW9udGggPT4gewogICAgICAgIGNhdGVnb3JpZXMuZm9yRWFjaChjYXRlZ29yeSA9PiB7CiAgICAgICAgICAvLyBHZW5lcmF0ZSByZWFsaXN0aWMgZmFpbCByYXRlIGRhdGEgKDAtNSUgcmFuZ2UpCiAgICAgICAgICBjb25zdCBiYXNlUmF0ZSA9IE1hdGgucmFuZG9tKCkgKiAzICsgMC41OyAvLyAwLjUlIHRvIDMuNSUKICAgICAgICAgIGNvbnN0IHZhcmlhdGlvbiA9IChNYXRoLnJhbmRvbSgpIC0gMC41KSAqIDE7IC8vIMKxMC41JQogICAgICAgICAgY29uc3QgZmFpbFJhdGUgPSBNYXRoLm1heCgwLjEsIGJhc2VSYXRlICsgdmFyaWF0aW9uKTsKCiAgICAgICAgICBkYXRhLnB1c2goewogICAgICAgICAgICBncm91cDogY2F0ZWdvcnksCiAgICAgICAgICAgIGtleTogbW9udGgsCiAgICAgICAgICAgIHZhbHVlOiBwYXJzZUZsb2F0KGZhaWxSYXRlLnRvRml4ZWQoMikpCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfSk7CgogICAgICByZXR1cm4gZGF0YTsKICAgIH0sCgogICAgYXN5bmMgbG9hZF9saW5lX2NoYXJ0KCkgewogICAgICB0cnkgewoKICAgICAgICBsZXQgdG9rZW4gPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLmdldFRva2VuOwogICAgICAgIGNvbnNvbGUubG9nKCJUT0tFTiIsIHRva2VuKQogICAgICAgIC8vIEZldGNoIGRhdGEgZnJvbSB0aGUgQVBJCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChwcm9jZXNzLmVudi5WVUVfQVBQX0FQSV9QQVRIICsgImdldF9mYWlsX2NvdW50X2J5X2NhdGVnb3J5X2RiIiwgewogICAgICAgICAgbWV0aG9kOiAiUE9TVCIsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICJDb250ZW50LVR5cGUiOiAiYXBwbGljYXRpb24vanNvbiIsCiAgICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIHRva2VuLAogICAgICAgICAgfSwKICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgUE46IHRoaXMuc2VsZWN0ZWRQTiwgY2F0ZWdvcnk6IHRoaXMuc2VsZWN0ZWRDYXRlZ29yeSwgYXJlYTogdGhpcy5zZWxlY3RlZEFyZWEgfSksCiAgICAgICAgfSk7CgogICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkgewogICAgICAgICAgY29uc29sZS5lcnJvcigiVW5hdXRob3JpemVkOiBDaGVjayB5b3VyIHRva2VuIG9yIGNyZWRlbnRpYWxzLiIpOwogICAgICAgIH0KCiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHRoaXMuaGFuZGxlUmVzcG9uc2UocmVzcG9uc2UpOwoKICAgICAgICBpZiAoZGF0YS5zdGF0dXNfcmVzID09PSAic3VjY2VzcyIpIHsKICAgICAgICAgIC8vIFVzZSBmdWxsX2RhdGEgaWYgYXZhaWxhYmxlIChQaGFzZTFCIGZvcm1hdCksIG90aGVyd2lzZSB1c2UgY291bnRzX2J5X3BlcmlvZCAobGVnYWN5IGZvcm1hdCkKICAgICAgICAgIGlmIChkYXRhLmZ1bGxfZGF0YSAmJiBkYXRhLmZ1bGxfZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCJVc2luZyBmdWxsIGRhdGEgZm9ybWF0OiIsIGRhdGEuZnVsbF9kYXRhKTsKICAgICAgICAgICAgLy8gVHJhbnNmb3JtIGZ1bGwgZGF0YSB0byBjaGFydCBmb3JtYXQKICAgICAgICAgICAgdGhpcy5saW5lX2NoYXJ0X2RhdGEgPSB0aGlzLnRyYW5zZm9ybUZ1bGxEYXRhVG9DaGFydEZvcm1hdChkYXRhLmZ1bGxfZGF0YSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBjb25zb2xlLmxvZygiVXNpbmcgbGVnYWN5IGNvdW50c19ieV9wZXJpb2QgZm9ybWF0OiIsIGRhdGEuY291bnRzX2J5X3BlcmlvZCk7CiAgICAgICAgICAgIHRoaXMubGluZV9jaGFydF9kYXRhID0gZGF0YS5jb3VudHNfYnlfcGVyaW9kOwogICAgICAgICAgfQogICAgICAgICAgY29uc29sZS5sb2coIkxpbmUgY2hhcnQgZGF0YToiLCB0aGlzLmxpbmVfY2hhcnRfZGF0YSk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoIkVycm9yIGxvYWRpbmcgZGF0YToiLCBlcnJvcik7CiAgICAgIH1maW5hbGx5IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsgIC8vIFNldCBsb2FkaW5nIHRvIGZhbHNlIG9uY2UgZGF0YSBpcyBsb2FkZWQKICAgICAgfQogICAgfSwKCiAgICBhc3luYyBsb2FkX2Jhcl9jaGFydCgpIHsKICAgICAgdHJ5IHsKICAgICAgICBsZXQgdG9rZW4gPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLmdldFRva2VuOwogICAgICAgIGNvbnNvbGUubG9nKCJUT0tFTiIsIHRva2VuKQogICAgICAgIC8vIEZldGNoIGRhdGEgZnJvbSB0aGUgQVBJCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChwcm9jZXNzLmVudi5WVUVfQVBQX0FQSV9QQVRIICsgImdldF9mYWlsX2NvdW50X2J5X2NhdGVnb3J5X2RiIiwgewogICAgICAgICAgbWV0aG9kOiAiUE9TVCIsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICJDb250ZW50LVR5cGUiOiAiYXBwbGljYXRpb24vanNvbiIsCiAgICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIHRva2VuLAogICAgICAgICAgfSwKICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtQTjogdGhpcy5zZWxlY3RlZFBOLCBjYXRlZ29yeTogdGhpcy5zZWxlY3RlZENhdGVnb3J5LCBhcmVhOiB0aGlzLnNlbGVjdGVkQXJlYSwgc3RhcnREYXRlOiB0aGlzLnNlbGVjdGVkU3RhcnREYXRlLCBlbmREYXRlOiB0aGlzLnNlbGVjdGVkRW5kRGF0ZX0pLAogICAgICAgIH0pOwoKICAgICAgICBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSA0MDEpIHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIlVuYXV0aG9yaXplZDogQ2hlY2sgeW91ciB0b2tlbiBvciBjcmVkZW50aWFscy4iKTsKICAgICAgICB9CgogICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCB0aGlzLmhhbmRsZVJlc3BvbnNlKHJlc3BvbnNlKTsKCiAgICAgICAgaWYgKGRhdGEuc3RhdHVzX3JlcyA9PT0gInN1Y2Nlc3MiKSB7CiAgICAgICAgICAvLyBVc2UgZnVsbF9kYXRhIGlmIGF2YWlsYWJsZSAoUGhhc2UxQiBmb3JtYXQpLCBvdGhlcndpc2UgdXNlIGNvdW50c19ieV9wZXJpb2QgKGxlZ2FjeSBmb3JtYXQpCiAgICAgICAgICBpZiAoZGF0YS5mdWxsX2RhdGEgJiYgZGF0YS5mdWxsX2RhdGEubGVuZ3RoID4gMCkgewogICAgICAgICAgICBjb25zb2xlLmxvZygiVXNpbmcgZnVsbCBkYXRhIGZvcm1hdCBmb3IgYmFyIGNoYXJ0OiIsIGRhdGEuZnVsbF9kYXRhKTsKICAgICAgICAgICAgLy8gVHJhbnNmb3JtIGZ1bGwgZGF0YSB0byBjaGFydCBmb3JtYXQKICAgICAgICAgICAgY29uc3QgdHJhbnNmb3JtZWREYXRhID0gdGhpcy50cmFuc2Zvcm1GdWxsRGF0YVRvQ2hhcnRGb3JtYXQoZGF0YS5mdWxsX2RhdGEpOwogICAgICAgICAgICB0aGlzLmJhcl9jaGFydF9kYXRhID0gdHJhbnNmb3JtZWREYXRhOwogICAgICAgICAgICB0aGlzLmxpbmVfY2hhcnRfZGF0YSA9IHRyYW5zZm9ybWVkRGF0YTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCJVc2luZyBsZWdhY3kgY291bnRzX2J5X3BlcmlvZCBmb3JtYXQgZm9yIGJhciBjaGFydDoiLCBkYXRhLmNvdW50c19ieV9wZXJpb2QpOwogICAgICAgICAgICB0aGlzLmJhcl9jaGFydF9kYXRhID0gZGF0YS5jb3VudHNfYnlfcGVyaW9kOwogICAgICAgICAgICB0aGlzLmxpbmVfY2hhcnRfZGF0YSA9IGRhdGEuY291bnRzX2J5X3BlcmlvZDsKICAgICAgICAgIH0KICAgICAgICAgIGNvbnNvbGUubG9nKCJCYXIgY2hhcnQgZGF0YToiLCB0aGlzLmJhcl9jaGFydF9kYXRhKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcigiRXJyb3IgbG9hZGluZyBkYXRhOiIsIGVycm9yKTsKICAgICAgfWZpbmFsbHkgewogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOyAgLy8gU2V0IGxvYWRpbmcgdG8gZmFsc2Ugb25jZSBkYXRhIGlzIGxvYWRlZAogICAgICB9CiAgICB9LAoKICAgIGFzeW5jIGdldF9jYXRlZ29yaWVzKCkgewogICAgICB0cnkgewogICAgICAgIGxldCB1c2VyX3R5cGUgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLmdldFVzZXJfdHlwZTsKICAgICAgICBsZXQgYWN0aW9uID0gInZpZXciOwogICAgICAgIGxldCB0b2tlbiA9IHRoaXMuJHN0b3JlLmdldHRlcnMuZ2V0VG9rZW47CiAgICAgICAgY29uc29sZS5sb2coIlRPS0VOIiwgdG9rZW4pCiAgICAgICAgLy8gRmV0Y2ggZGF0YSBmcm9tIHRoZSBBUEkKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHByb2Nlc3MuZW52LlZVRV9BUFBfQVBJX1BBVEggKyAiZ2V0X3VuaXF1ZV9jYXRlZ29yaWVzX2RiIiwgewogICAgICAgICAgbWV0aG9kOiAiUE9TVCIsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICJDb250ZW50LVR5cGUiOiAiYXBwbGljYXRpb24vanNvbiIsCiAgICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIHRva2VuLAogICAgICAgICAgfSwKICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgIlBOIjogdGhpcy5zZWxlY3RlZFBOLCB1c2VyX3R5cGUsIGFjdGlvbiB9KSwKICAgICAgICB9KTsKCiAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxKSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCJVbmF1dGhvcml6ZWQ6IENoZWNrIHlvdXIgdG9rZW4gb3IgY3JlZGVudGlhbHMuIik7CiAgICAgICAgfQoKICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgdGhpcy5oYW5kbGVSZXNwb25zZShyZXNwb25zZSk7CgogICAgICAgIGlmIChkYXRhLnN0YXR1c19yZXMgPT09ICJzdWNjZXNzIikgewogICAgICAgICAgdGhpcy5jYXRlZ29yeU9wdGlvbnMgPSBkYXRhLmFsbF9jYXRlZ29yaWVzOwogICAgICAgICAgdGhpcy5hcmVhT3B0aW9ucyA9IGRhdGEuYWxsX3N0YWdlczsKICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMuYXJlYU9wdGlvbnMpCiAgICAgICAgICBjb25zb2xlLmxvZygiUmVjZWl2ZWQgZGF0YToiLCBkYXRhKTsKCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoIkVycm9yIGxvYWRpbmcgZGF0YToiLCBlcnJvcik7CiAgICAgIH0KICAgIH0sCgogICAgYXN5bmMgZ2V0X1BOcygpIHsKICAgICAgdHJ5IHsKICAgICAgICBsZXQgdG9rZW4gPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLmdldFRva2VuOwogICAgICAgIGNvbnNvbGUubG9nKCJUT0tFTiIsIHRva2VuKQogICAgICAgIC8vIEZldGNoIGRhdGEgZnJvbSB0aGUgQVBJCiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChwcm9jZXNzLmVudi5WVUVfQVBQX0FQSV9QQVRIICsgImdldF91bmlxdWVfcG5zIiwgewogICAgICAgICAgbWV0aG9kOiAiUE9TVCIsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICJDb250ZW50LVR5cGUiOiAiYXBwbGljYXRpb24vanNvbiIsCiAgICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIHRva2VuLAogICAgICAgICAgfSwKICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHt9KSwKICAgICAgICB9KTsKCiAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxKSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCJVbmF1dGhvcml6ZWQ6IENoZWNrIHlvdXIgdG9rZW4gb3IgY3JlZGVudGlhbHMuIik7CiAgICAgICAgfQoKICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgdGhpcy5oYW5kbGVSZXNwb25zZShyZXNwb25zZSk7CgogICAgICAgIGlmIChkYXRhLnN0YXR1c19yZXMgPT09ICJzdWNjZXNzIikgewogICAgICAgICAgdGhpcy5wbk9wdGlvbnMgPSBkYXRhLmFsbF9wbnM7CgogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCJFcnJvciBsb2FkaW5nIGRhdGE6IiwgZXJyb3IpOwogICAgICB9CiAgICB9LAoKICAgIGFzeW5jIGdldF9jYXRlZ29yaWVzMihjaGFuZ2VkVmFyKSB7CiAgICAgIHRyeSB7CiAgICAgICAgbGV0IHVzZXJfdHlwZSA9IHRoaXMuJHN0b3JlLmdldHRlcnMuZ2V0VXNlcl90eXBlOwogICAgICAgIGxldCBhY3Rpb24gPSAidmlldyI7CiAgICAgICAgbGV0IGFyZWEgPSBudWxsOwogICAgICAgIGxldCBjYXRlZ29yeSA9IG51bGw7CgogICAgICAgIGlmIChjaGFuZ2VkVmFyID09PSAiYXJlYSIpewogICAgICAgICAgYXJlYSA9IHRoaXMuc2VsZWN0ZWRBcmVhCiAgICAgICAgfWVsc2UgaWYgKGNoYW5nZWRWYXIgPT09ICJjYXRlZ29yeSIpewogICAgICAgICAgY2F0ZWdvcnkgPSB0aGlzLnNlbGVjdGVkQ2F0ZWdvcnkKICAgICAgICB9CiAgICAgICAgbGV0IHRva2VuID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5nZXRUb2tlbjsKICAgICAgICBjb25zb2xlLmxvZygiVE9LRU4iLCB0b2tlbikKICAgICAgICAvLyBGZXRjaCBkYXRhIGZyb20gdGhlIEFQSQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2gocHJvY2Vzcy5lbnYuVlVFX0FQUF9BUElfUEFUSCArICJnZXRfdW5pcXVlX2NhdGVnb3JpZXNfZGIyIiwgewogICAgICAgICAgbWV0aG9kOiAiUE9TVCIsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICJDb250ZW50LVR5cGUiOiAiYXBwbGljYXRpb24vanNvbiIsCiAgICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIHRva2VuLAogICAgICAgICAgfSwKICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgIlBOIjogdGhpcy5zZWxlY3RlZFBOLCAiYXJlYSI6IGFyZWEsICJjYXRlZ29yeSI6IGNhdGVnb3J5LCB1c2VyX3R5cGUsIGFjdGlvbiB9KSwKICAgICAgICB9KTsKCiAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxKSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCJVbmF1dGhvcml6ZWQ6IENoZWNrIHlvdXIgdG9rZW4gb3IgY3JlZGVudGlhbHMuIik7CiAgICAgICAgfQoKICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgdGhpcy5oYW5kbGVSZXNwb25zZShyZXNwb25zZSk7CgogICAgICAgIGlmIChkYXRhLnN0YXR1c19yZXMgPT09ICJzdWNjZXNzIikgewogICAgICAgICAgaWYgKGNoYW5nZWRWYXIgPT09ICJhcmVhIil7CiAgICAgICAgICAgIHRoaXMuY2F0ZWdvcnlPcHRpb25zID0gZGF0YS5uZXdfYXJyYXk7CiAgICAgICAgICB9IGVsc2UgaWYgKGNoYW5nZWRWYXIgPT09ICJjYXRlZ29yeSIpewogICAgICAgICAgICB0aGlzLmFyZWFPcHRpb25zID0gZGF0YS5uZXdfYXJyYXk7CiAgICAgICAgICB9CgogICAgICAgICAgY29uc29sZS5sb2coIlJlY2VpdmVkIGRhdGE6IiwgZGF0YSk7CgogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCJFcnJvciBsb2FkaW5nIGRhdGE6IiwgZXJyb3IpOwogICAgICB9CiAgICB9LAoKICAgIGhhbmRsZVJvb3RDYXVzZVZpZXdCeUNoYW5nZSgpIHsKICAgICAgY29uc29sZS5sb2coYFJvb3QgY2F1c2UgdGltZSByYW5nZSBjaGFuZ2VkIHRvOiAke3RoaXMucm9vdENhdXNlVGltZVJhbmdlfWApOwogICAgICB0aGlzLmxvYWRSb290Q2F1c2VEYXRhKCk7CiAgICB9LAoKICAgIGhhbmRsZVJvb3RDYXVzZVRpbWVSYW5nZUNoYW5nZSgpIHsKICAgICAgY29uc29sZS5sb2coYFJvb3QgY2F1c2UgdGltZSByYW5nZSBjaGFuZ2VkIHRvOiAke3RoaXMucm9vdENhdXNlVGltZVJhbmdlfWApOwogICAgICB0aGlzLmxvYWRSb290Q2F1c2VEYXRhKCk7CiAgICB9LAoKICAgIGhhbmRsZVJvb3RDYXVzZUdyb3VwQ2hhbmdlKCkgewogICAgICBjb25zb2xlLmxvZyhgUm9vdCBjYXVzZSBncm91cCBjaGFuZ2VkIHRvOiAke3RoaXMucm9vdENhdXNlU2VsZWN0ZWRHcm91cH1gKTsKICAgICAgY29uc29sZS5sb2coJ0N1cnJlbnQgY2hhcnQgZGF0YSBsZW5ndGggYmVmb3JlIHJlbG9hZDonLCB0aGlzLnJvb3RDYXVzZUNoYXJ0RGF0YS5sZW5ndGgpOwogICAgICB0aGlzLmxvYWRSb290Q2F1c2VEYXRhKCk7CiAgICB9LAoKICAgIGdldEF1dGhDb25maWcoKSB7CiAgICAgIC8vIEdldCBhdXRoZW50aWNhdGlvbiB0b2tlbiBmcm9tIGxvY2FsU3RvcmFnZSBvciBWdWV4IHN0b3JlCiAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJykgfHwgdGhpcy4kc3RvcmUuc3RhdGUudG9rZW47CgogICAgICByZXR1cm4gewogICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAKICAgICAgICB9CiAgICAgIH07CiAgICB9LAoKICAgIGhhbmRsZVJlc3BvbnNlKHJlc3BvbnNlKSB7CiAgICAgIGlmICghcmVzcG9uc2Uub2spIHsKICAgICAgICBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSA0MDEpIHsKICAgICAgICAgIHRoaXMuc2Vzc2lvbl9leHBpcmVkX3Zpc2libGUgPSB0cnVlOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gcmVzcG9uc2UuanNvbigpOwogICAgICB9CiAgICB9LAoKICAgIGhhbmRsZUJhckNsaWNrKGRhdGEpIHsKICAgICAgLy8gVXBkYXRlIHRoZSBjaGFydCBkYXRhIHRvIG9ubHkgc2hvdyB0aGUgY2xpY2tlZCBiYXIKICAgICAgY29uc29sZS5sb2coIkJhciBkYXRhIHJlY2VpdmVkOiIsIGRhdGEpOwogICAgICAvLyB0aGlzLmJhcl9jaGFydF9kYXRhID0gW2RhdGFdOwogICAgfSwKCiAgICBoYW5kbGVQb2ludENsaWNrKGRhdGEpIHsKICAgICAgY29uc29sZS5sb2coIlBvaW50IGNsaWNrZWQ6IiwgZGF0YSk7CiAgICAgIC8vIE5hdmlnYXRlIHRvIGEgbmV3IHBhZ2Ugb3IgZGlzcGxheSBhIG1lc3NhZ2UKICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBuYW1lOiAnSGVsbG9QYWdlJyB9KTsKICAgIH0sCgogICAgaGFuZGxlUXVlcnlNZXRob2RDaGFuZ2UobmV3VmFsKSB7CiAgICAgIC8vIGNvbnNvbGUubG9nKCJRdWVyeSBtZXRob2QgY2hhbmdlZDoiLCBuZXdWYWwpOwogICAgICB0aGlzLnF1ZXJ5TWV0aG9kID0gbmV3VmFsOwogICAgICB0aGlzLmNsZWFyUmVzdWx0cygpOwogICAgfSwKCiAgICBoYW5kbGVRdWVyeVR5cGVDaGFuZ2UobmV3VmFsKSB7CiAgICAgIC8vIGNvbnNvbGUubG9nKCJRdWVyeSBtZXRob2QgY2hhbmdlZDoiLCBuZXdWYWwpOwogICAgICB0aGlzLnF1ZXJ5VHlwZSA9IG5ld1ZhbDsKICAgICAgdGhpcy5jbGVhclJlc3VsdHMoKTsKICAgIH0sCgogICAgaGFuZGxlU3VibWl0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICB0aGlzLmFpUXVlcnkgPSAnJzsKICAgICAgdGhpcy5iYXJfY2hhcnRfZGF0YSA9IFtdOwogICAgICB0aGlzLmxpbmVfY2hhcnRfZGF0YSA9IFtdOwogICAgICBjb25zb2xlLmxvZygnUE46JywgdGhpcy5zZWxlY3RlZFBOKTsKICAgICAgY29uc29sZS5sb2coJ0FyZWE6JywgdGhpcy5zZWxlY3RlZEFyZWEpOwogICAgICBjb25zb2xlLmxvZygnQ2F0ZWdvcnk6JywgdGhpcy5zZWxlY3RlZENhdGVnb3J5KTsKICAgICAgY29uc29sZS5sb2coJ0NoYXJ0IFR5cGU6JywgdGhpcy5zZWxlY3RlZENoYXJ0VHlwZSk7CiAgICAgIGNvbnNvbGUubG9nKGBEYXRlIFJhbmdlOiBTdGFydDogJHt0aGlzLnNlbGVjdGVkRGF0ZX1gLCApOwogICAgICB0aGlzLnNlbGVjdGVkU3RhcnREYXRlID0gdGhpcy5zZWxlY3RlZERhdGVSYW5nZS5zdGFydERhdGU7CiAgICAgIHRoaXMuc2VsZWN0ZWRFbmREYXRlID0gdGhpcy5zZWxlY3RlZERhdGVSYW5nZS5lbmREYXRlOwogICAgICAvLyBVcGRhdGUgY2hhcnQgZGF0YSBiYXNlZCBvbiBzZWxlY3RlZCBjaGFydCB0eXBlCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkQ2hhcnRUeXBlID09PSAnQmFyJykgewogICAgICAgIC8vdGhpcy5jaGFydERhdGEgPSB0aGlzLmJhcl9jaGFydF9kYXRhOwogICAgICAgIHRoaXMuZGlzcGxheUNoYXJ0VHlwZSA9ICdCYXInOwogICAgICAgIHRoaXMubG9hZF9iYXJfY2hhcnQoKQogICAgICB9IGVsc2UgaWYgKHRoaXMuc2VsZWN0ZWRDaGFydFR5cGUgPT09ICdMaW5lJykgewogICAgICAgIC8vIHRoaXMuY2hhcnREYXRhID0gdGhpcy5saW5lX2NoYXJ0X2RhdGE7CiAgICAgICAgdGhpcy5kaXNwbGF5Q2hhcnRUeXBlID0gJ0xpbmUnOwogICAgICAgIHRoaXMubG9hZF9saW5lX2NoYXJ0KCkKICAgICAgfQogICAgfSwKCiAgICBhc3luYyBsb2FkU3VwcGxpZXJzKCkgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IHRva2VuID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5nZXRUb2tlbjsKICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHByb2Nlc3MuZW52LlZVRV9BUFBfQVBJX1BBVEggKyAiZ2V0X3N1cHBsaWVycyIsIHsKICAgICAgICAgIG1ldGhvZDogIlBPU1QiLAogICAgICAgICAgaGVhZGVyczogewogICAgICAgICAgICAiQ29udGVudC1UeXBlIjogImFwcGxpY2F0aW9uL2pzb24iLAogICAgICAgICAgICBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyB0b2tlbiwKICAgICAgICAgIH0sCiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7fSksCiAgICAgICAgfSk7CgogICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCB0aGlzLmhhbmRsZVJlc3BvbnNlKHJlc3BvbnNlKTsKICAgICAgICBpZiAoZGF0YS5zdGF0dXNfcmVzID09PSAic3VjY2VzcyIpIHsKICAgICAgICAgIHRoaXMuc3VwcGxpZXJPcHRpb25zID0gZGF0YS5zdXBwbGllcnMubWFwKHN1cHBsaWVyID0+ICh7CiAgICAgICAgICAgIGxhYmVsOiBzdXBwbGllciwKICAgICAgICAgICAgdmFsdWU6IHN1cHBsaWVyCiAgICAgICAgICB9KSk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoIkVycm9yIGxvYWRpbmcgc3VwcGxpZXJzOiIsIGVycm9yKTsKICAgICAgfQogICAgfSwKCiAgICBhc3luYyBsb2FkVmludGFnZXMoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgdG9rZW4gPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLmdldFRva2VuOwogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2gocHJvY2Vzcy5lbnYuVlVFX0FQUF9BUElfUEFUSCArICJnZXRfdmludGFnZXMiLCB7CiAgICAgICAgICBtZXRob2Q6ICJQT1NUIiwKICAgICAgICAgIGhlYWRlcnM6IHsKICAgICAgICAgICAgIkNvbnRlbnQtVHlwZSI6ICJhcHBsaWNhdGlvbi9qc29uIiwKICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgdG9rZW4sCiAgICAgICAgICB9LAogICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe30pLAogICAgICAgIH0pOwoKICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgdGhpcy5oYW5kbGVSZXNwb25zZShyZXNwb25zZSk7CiAgICAgICAgaWYgKGRhdGEuc3RhdHVzX3JlcyA9PT0gInN1Y2Nlc3MiKSB7CiAgICAgICAgICB0aGlzLnZpbnRhZ2VPcHRpb25zID0gZGF0YS52aW50YWdlcy5tYXAodmludGFnZSA9PiAoewogICAgICAgICAgICBsYWJlbDogdmludGFnZSwKICAgICAgICAgICAgdmFsdWU6IHZpbnRhZ2UKICAgICAgICAgIH0pKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcigiRXJyb3IgbG9hZGluZyB2aW50YWdlczoiLCBlcnJvcik7CiAgICAgIH0KICAgIH0sCgogICAgYXN5bmMgaGFuZGxlQ29tYm9TdWJtaXQoKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMuZGlzcGxheUNoYXJ0VHlwZSA9ICdDb21ibyc7CiAgICAgIHRoaXMuY29tYm9fY2hhcnRfZGF0YSA9IFtdOwogICAgICB0aGlzLmNvbWJvX3RhYmxlX2RhdGEgPSBbXTsKCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgdG9rZW4gPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLmdldFRva2VuOwogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2gocHJvY2Vzcy5lbnYuVlVFX0FQUF9BUElfUEFUSCArICJnZXRfY29tYm9fZGVmcmF0ZV9kYXRhIiwgewogICAgICAgICAgbWV0aG9kOiAiUE9TVCIsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICJDb250ZW50LVR5cGUiOiAiYXBwbGljYXRpb24vanNvbiIsCiAgICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIHRva2VuLAogICAgICAgICAgfSwKICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsKICAgICAgICAgICAgc3RhcnREYXRlOiB0aGlzLmNvbWJvRGF0ZVJhbmdlLnN0YXJ0RGF0ZSwKICAgICAgICAgICAgZW5kRGF0ZTogdGhpcy5jb21ib0RhdGVSYW5nZS5lbmREYXRlLAogICAgICAgICAgICBzdXBwbGllcnM6IHRoaXMuc2VsZWN0ZWRTdXBwbGllcnMubWFwKHMgPT4gcy52YWx1ZSksCiAgICAgICAgICAgIHBhcnROdW1iZXJzOiB0aGlzLnNlbGVjdGVkUGFydE51bWJlcnMubWFwKHBuID0+IHBuLnZhbHVlKSwKICAgICAgICAgICAgdmludGFnZXM6IHRoaXMuc2VsZWN0ZWRWaW50YWdlcy5tYXAodiA9PiB2LnZhbHVlKQogICAgICAgICAgfSksCiAgICAgICAgfSk7CgogICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCB0aGlzLmhhbmRsZVJlc3BvbnNlKHJlc3BvbnNlKTsKICAgICAgICBpZiAoZGF0YS5zdGF0dXNfcmVzID09PSAic3VjY2VzcyIpIHsKICAgICAgICAgIHRoaXMuY29tYm9fY2hhcnRfZGF0YSA9IGRhdGEuY2hhcnRfZGF0YTsKICAgICAgICAgIHRoaXMuY29tYm9fdGFibGVfZGF0YSA9IGRhdGEudGFibGVfZGF0YTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcigiRXJyb3IgbG9hZGluZyBjb21ibyBkYXRhOiIsIGVycm9yKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKCiAgICBhc3luYyBoYW5kbGVDb21wYXJlU3VibWl0KCkgewogICAgICBpZiAoIXRoaXMuY29tcGFyZVBOMSB8fCAhdGhpcy5jb21wYXJlUE4yKSB7CiAgICAgICAgYWxlcnQoIlBsZWFzZSBzZWxlY3QgYm90aCBwYXJ0IG51bWJlcnMgZm9yIGNvbXBhcmlzb24iKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMuZGlzcGxheUNoYXJ0VHlwZSA9ICdDb21wYXJlJzsKICAgICAgdGhpcy5jb21wYXJlX2NoYXJ0X2RhdGEgPSBbXTsKICAgICAgdGhpcy5jb21wYXJpc29uX3N1bW1hcnkgPSBbXTsKCiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgdG9rZW4gPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLmdldFRva2VuOwogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2gocHJvY2Vzcy5lbnYuVlVFX0FQUF9BUElfUEFUSCArICJnZXRfY29tcGFyZV9kYXRhIiwgewogICAgICAgICAgbWV0aG9kOiAiUE9TVCIsCiAgICAgICAgICBoZWFkZXJzOiB7CiAgICAgICAgICAgICJDb250ZW50LVR5cGUiOiAiYXBwbGljYXRpb24vanNvbiIsCiAgICAgICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIHRva2VuLAogICAgICAgICAgfSwKICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsKICAgICAgICAgICAgcGFydE51bWJlcjE6IHRoaXMuY29tcGFyZVBOMSwKICAgICAgICAgICAgcGFydE51bWJlcjI6IHRoaXMuY29tcGFyZVBOMiwKICAgICAgICAgICAgc3RhcnREYXRlOiB0aGlzLmNvbXBhcmVEYXRlUmFuZ2Uuc3RhcnREYXRlLAogICAgICAgICAgICBlbmREYXRlOiB0aGlzLmNvbXBhcmVEYXRlUmFuZ2UuZW5kRGF0ZQogICAgICAgICAgfSksCiAgICAgICAgfSk7CgogICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCB0aGlzLmhhbmRsZVJlc3BvbnNlKHJlc3BvbnNlKTsKICAgICAgICBpZiAoZGF0YS5zdGF0dXNfcmVzID09PSAic3VjY2VzcyIpIHsKICAgICAgICAgIHRoaXMuY29tcGFyZV9jaGFydF9kYXRhID0gZGF0YS5jaGFydF9kYXRhOwoKICAgICAgICAgIC8vIFByb2Nlc3MgY29tcGFyaXNvbiBzdW1tYXJ5CiAgICAgICAgICB0aGlzLmNvbXBhcmlzb25fc3VtbWFyeSA9IGRhdGEuY29tcGFyaXNvbl9kYXRhLm1hcChpdGVtID0+ICh7CiAgICAgICAgICAgIHBhcnROdW1iZXI6IGl0ZW0ucGFydE51bWJlciwKICAgICAgICAgICAgdG90YWxEZWZlY3RzOiBpdGVtLnRvdGFsRGVmZWN0cywKICAgICAgICAgICAgdG9wUm9vdENhdXNlOiBPYmplY3Qua2V5cyhpdGVtLnJvb3RDYXVzZXMpLnJlZHVjZSgoYSwgYikgPT4KICAgICAgICAgICAgICBpdGVtLnJvb3RDYXVzZXNbYV0gPiBpdGVtLnJvb3RDYXVzZXNbYl0gPyBhIDogYgogICAgICAgICAgICApCiAgICAgICAgICB9KSk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoIkVycm9yIGxvYWRpbmcgY29tcGFyaXNvbiBkYXRhOiIsIGVycm9yKTsKICAgICAgfSBmaW5hbGx5IHsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfQogICAgfSwKICB9Cn07Cg=="}, {"version": 3, "sources": ["Phase1B.vue"], "names": [], "mappings": ";AA8g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file": "Phase1B.vue", "sourceRoot": "src/views/Phase1B", "sourcesContent": ["<style scoped>\n.short-dropdown {\n  max-width: 200px; /* Adjust the width as needed */\n}\n\n.submit-button {\n  margin-top: 20px; /* Add some spacing above the button */\n}\n\n.flex-container {\n  display: flex;\n  align-items: flex-start;\n  gap: 24px;\n  padding: 16px;\n  min-height: calc(100vh - 120px);\n}\n\n.left-column {\n  flex: 0 0 350px;\n  /* background-color: #f4f4f4; */\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.right-column {\n  flex: 1;\n  min-width: 0;\n}\n\n.ai-query-section {\n  margin-bottom: 20px;\n  padding: 15px;\n \n  /* background-color: #f4f4f400;  */\n}\n\n/* .ai-query-label {\n  font-weight: 600;\n  margin-bottom: 10px;\n  color: #e3e3e3;\n} */\n\n.ai-textarea {\n  width: 100%;\n  margin-bottom: 15px;\n}\n\n.ai-buttons {\n  display: flex;\n  gap: 10px;\n}\n\n.query-method-toggle {\n  margin-bottom: 24px;\n  padding: 16px;\n\n  border-radius: 6px;\n\n}\n\n.manual-controls {\n  margin-top: 20px;\n  padding: 16px;\n\n  border-radius: 6px;\n\n}\n\n.combo-controls {\n  margin-top: 20px;\n  padding: 20px;\n\n  border-radius: 6px;\n \n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.combo-controls h3 {\n  margin-bottom: 20px;\n  color: #161616;\n  font-weight: 600;\n  font-size: 1.125rem;\n}\n\n.compare-controls {\n  margin-top: 20px;\n  padding: 20px;\n \n  border-radius: 6px;\n\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.compare-controls h3 {\n  margin-bottom: 20px;\n  color: #161616;\n  font-weight: 600;\n  font-size: 1.125rem;\n}\n\n.data-table-section {\n  margin-top: 20px;\n  padding: 15px;\n  border-radius: 4px;\n}\n\n.comparison-summary {\n  margin-top: 20px;\n}\n\n.summary-card {\n  margin-bottom: 10px;\n}\n\n.summary-card h6 {\n  margin-bottom: 5px;\n  font-weight: 600;\n}\n\n.phase1b-container {\n  /* background-color: #f4f4f4; */\n  min-height: 100vh;\n}\n\n.left-column h1 {\n  color: #161616;\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 24px;\n  border-bottom: 2px solid #0f62fe;\n  padding-bottom: 8px;\n}\n\n.short-dropdown {\n  margin-bottom: 16px;\n}\n\n.submit-button {\n  margin-top: 20px;\n  width: 100%;\n}\n\n.ai-query-section {\n  margin-bottom: 24px;\n  padding: 20px;\n  background-color: #ffffff;\n  border-radius: 6px;\n  border: 1px solid #e0e0e0;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.ai-query-label {\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: #161616;\n  font-size: 1rem;\n}\n\n.chart-controls {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.control-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.control-label {\n  font-weight: 500;\n  margin-bottom: 8px;\n  color: #525252;\n}\n\n.control-dropdown {\n  width: 100%;\n}\n</style>\n\n<template>\n  \n  <div class=\"phase1b-container\">\n    <MainHeader :expandedSideNav=false :useFixed=\"useFixed\" />\n    <div class=\"flex-container\">\n      <div class=\"left-column\">\n      <h1 style=\"color: #ffffff;\">Phase 1</h1>\n\n      <!-- Query Method Toggle -->\n      <div class=\"query-method-toggle\">\n        <cv-radio-group\n          v-model=\"queryMethod\"\n          @change=\"handleQueryMethodChange\"\n          label=\"Query Method\"\n          vertical\n        >\n          <cv-radio-button\n            name=\"queryMethod\"\n            label=\"AI Query\"\n            value=\"ai\"\n          />\n          <cv-radio-button\n            name=\"queryMethod\"\n            label=\"Dropdown Selection\"\n            value=\"manual\"\n          />\n        </cv-radio-group>\n      </div>\n\n      <!-- AI Query Section -->\n      <div v-if=\"queryMethod === 'ai'\" >\n        <div style=\"color: #ffffff;\">What would you like to view?</div>\n        <cv-text-area\n          v-model=\"aiQuery\"\n          placeholder=\"Example: Show me bar chart for PN ABC123 in area XYZ for category defects from 2024-01-01 to 2024-03-31\"\n          rows=\"4\"\n          class=\"ai-textarea\"\n        />\n        <div class=\"ai-buttons\">\n          <cv-button \n            @click=\"processAiQuery\" \n            :disabled=\"loading || !aiQuery.trim()\"\n            kind=\"primary\"\n          >\n            {{ loading ? 'Processing...' : 'Generate Query' }}\n          </cv-button>\n          <cv-button \n            @click=\"clearAiQuery\" \n            kind=\"secondary\"\n          >\n            Clear\n          </cv-button>\n        </div>\n      </div>\n\n      <!-- Manual Controls Section -->\n      <div v-if=\"queryMethod === 'manual'\" class=\"manual-controls\">\n        <cv-radio-group\n          v-model=\"queryType\"\n          @change=\"handleQueryTypeChange\"\n          label=\"Query Type\"\n          vertical\n        >\n          <cv-radio-button\n            name=\"queryType\"\n            label=\"Fab/Ful DefRate\"\n            value=\"fabful\"\n          />\n          <cv-radio-button\n            name=\"queryType\"\n            label=\"Pareto\"\n            value=\"pareto\"\n          />\n          <cv-radio-button\n            name=\"queryType\"\n            label=\"Combo DefRate\"\n            value=\"combo\"\n          />\n          <cv-radio-button\n            name=\"queryType\"\n            label=\"Compare\"\n            value=\"compare\"\n          />\n        </cv-radio-group>\n        <div v-if=\"queryType === 'fabful'\" >\n              \n\n              <div class=\"chart-controls\">\n                <div class=\"control-group\">\n                  <label class=\"control-label\">View By:</label>\n                  <cv-dropdown\n                    v-model=\"rootCauseViewBy\"\n                    @change=\"handleRootCauseViewByChange\"\n                    class=\"control-dropdown\"\n                  >\n                    <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n                    <cv-dropdown-item value=\"vintage\">Vintage</cv-dropdown-item>\n                    <cv-dropdown-item value=\"sector\">Sector</cv-dropdown-item>\n                    <cv-dropdown-item value=\"supplier\">Supplier</cv-dropdown-item>\n                    <cv-dropdown-item value=\"partNum\">Part Number</cv-dropdown-item>\n                  </cv-dropdown>\n                </div>\n\n                <div class=\"control-group\">\n                  <label class=\"control-label\">Time Range:</label>\n                  <cv-dropdown\n                    v-model=\"rootCauseTimeRange\"\n                    @change=\"handleRootCauseTimeRangeChange\"\n                    class=\"control-dropdown\"\n                  >\n                    <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n                    <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n                  </cv-dropdown>\n                </div>\n\n                <div class=\"control-group\" v-if=\"breakoutGroups.length > 0\">\n                  <label class=\"control-label\">Group:</label>\n                  <cv-dropdown\n                    v-model=\"rootCauseSelectedGroup\"\n                    @change=\"handleRootCauseGroupChange\"\n                    class=\"control-dropdown\"\n                  >\n                    <cv-dropdown-item value=\"all\">All Groups</cv-dropdown-item>\n                    <cv-dropdown-item\n                      v-for=\"group in breakoutGroups\"\n                      :key=\"group.name\"\n                      :value=\"group.name\"\n                    >\n                      {{ group.name }}\n                    </cv-dropdown-item>\n                  </cv-dropdown>\n                </div>\n              </div>\n\n              <div class=\"chart-container\">\n                <div v-if=\"isRootCauseDataLoading\" >\n                          Loading Chart...\n                          <RootCauseChart :data = [] :loading=\"isRootCauseDataLoading\"/>\n                        </div>\n                <RootCauseChart\n                 v-if=\"rootCauseChartData.length > 0 && !isRootCauseDataLoading\"\n                  :data=\"rootCauseChartData\"\n                  :viewBy=\"rootCauseViewBy\"\n                  :timeRange=\"rootCauseTimeRange\"\n                  :selectedGroup=\"rootCauseSelectedGroup\"\n                  :loading=\"isRootCauseDataLoading\"\n                  :title = \"rootCauseTitle\"\n                  @bar-click=\"handleRootCauseBarClick\"\n                />\n\n                <div v-if=\"rootCauseChartData.length == 0 && !isRootCauseDataLoading\" >\n                          No data available\n                </div>\n              </div>\n            \n              \n      </div>\n        <div v-if=\"queryType === 'combo'\" >\n          <!-- Combo DefRate Controls -->\n          <div class=\"combo-controls\">\n            <h3>Combo DefRate Analysis</h3>\n\n            <!-- Date Range -->\n            <cv-date-picker\n              v-model=\"comboDateRange\"\n              label=\"Date Range\"\n              kind=\"range\"\n              :cal-options=\"calOptions\"\n              placeholder=\"yyyy-mm-dd\"\n            />\n\n            <!-- Suppliers Multi-Select -->\n            <cv-multi-select\n              v-model=\"selectedSuppliers\"\n              label=\"Suppliers\"\n              :options=\"supplierOptions\"\n              class=\"short-dropdown\"\n            />\n\n            <!-- Part Numbers Multi-Select -->\n            <cv-multi-select\n              v-model=\"selectedPartNumbers\"\n              label=\"Part Numbers\"\n              :options=\"partNumberOptions\"\n              class=\"short-dropdown\"\n            />\n\n            <!-- Vintages Multi-Select -->\n            <cv-multi-select\n              v-model=\"selectedVintages\"\n              label=\"Vintages\"\n              :options=\"vintageOptions\"\n              class=\"short-dropdown\"\n            />\n\n            <!-- Submit Button -->\n            <cv-button @click=\"handleComboSubmit\" class=\"submit-button\" kind=\"primary\">\n              Generate Combo Chart\n            </cv-button>\n          </div>\n        </div>\n\n        <div v-if=\"queryType === 'pareto'\" >\n        \n        \n        <!-- PN Dropdown -->\n        <cv-combo-box class=\"short-dropdown\"\n          v-model=\"selectedPN\"\n          label=\"PN\"\n          :options=\"pnOptions\"\n        ></cv-combo-box>\n\n        <!-- Area Dropdown -->\n        <cv-dropdown class=\"short-dropdown\"\n          v-model=\"selectedArea\"\n          label=\"Area\"\n          :items=\"areaOptions\"\n        ></cv-dropdown>\n\n        <!-- Category Dropdown -->\n        <cv-dropdown class=\"short-dropdown\"\n          v-model=\"selectedCategory\"\n          label=\"Category\"\n          :items=\"categoryOptions\"\n        ></cv-dropdown>\n\n         <!-- Date Select -->\n         <cv-date-picker\n          v-model=\"selectedDateRange\"\n          label=\"Choose Date Range\"\n          kind = 'range'\n          :cal-options = \"calOptions\"\n          placeholder = \"yyyy-mm-dd\"\n        ></cv-date-picker>\n\n        <!-- Chart Type Dropdown -->\n        <cv-dropdown class=\"short-dropdown\"\n          v-model=\"selectedChartType\"\n          label=\"Chart Type\"\n          :items=\"chartTypeOptions\"\n        ></cv-dropdown>\n\n        <!-- Submit Button -->\n        <cv-button @click=\"handleSubmit\" class=\"submit-button\">\n          Submit\n        </cv-button>\n      </div>\n\n      <div v-if=\"queryType === 'compare'\" >\n        <!-- Compare Section -->\n        <div class=\"compare-controls\">\n          <h3>Compare Part Numbers</h3>\n\n          <!-- Part Number 1 -->\n          <cv-combo-box\n            v-model=\"comparePN1\"\n            label=\"Part Number 1\"\n            :options=\"pnOptions\"\n            class=\"short-dropdown\"\n          />\n\n          <!-- Part Number 2 -->\n          <cv-combo-box\n            v-model=\"comparePN2\"\n            label=\"Part Number 2\"\n            :options=\"pnOptions\"\n            class=\"short-dropdown\"\n          />\n\n          <!-- Date Range for Comparison -->\n          <cv-date-picker\n            v-model=\"compareDateRange\"\n            label=\"Comparison Date Range\"\n            kind=\"range\"\n            :cal-options=\"calOptions\"\n            placeholder=\"yyyy-mm-dd\"\n          />\n\n          <!-- Submit Button -->\n          <cv-button @click=\"handleCompareSubmit\" class=\"submit-button\" kind=\"primary\">\n            Compare Performance\n          </cv-button>\n        </div>\n      </div>\n      </div>\n\n    </div>\n\n    <div class=\"right-column\">\n      <cv-tile :light=\"lightTile\">\n        <!-- Show loading skeletons while data is being fetched -->\n        <div v-if=\"loading && displayChartType === 'Bar'\">\n          Loading Bar Chart...\n          <BarChart :data = [] :loading=\"loading\"/>\n        </div>\n        <div v-if=\"loading && displayChartType === 'Line'\" >\n          Loading Line Chart...\n          <LineChart :data = [] :loading=\"loading\"/>\n        </div>\n        <div v-if=\"loading && displayChartType === 'Combo'\" >\n          Loading Combo Chart...\n          <ComboChart :data = [] :loading=\"loading\"/>\n        </div>\n\n        <!-- Show charts after data is loaded -->\n        <BarChart v-if=\"displayChartType === 'Bar' && bar_chart_data.length > 0\" :data=\"bar_chart_data\" @bar-clicked=\"handleBarClick\" :loading=\"loading\"/>\n        <LineChart v-if=\"displayChartType === 'Line' && line_chart_data.length > 0\" :data=\"line_chart_data\" @point-clicked=\"handlePointClick\" :loading=\"loading\"/>\n        <ComboChart v-if=\"displayChartType === 'Combo' && combo_chart_data.length > 0\" :data=\"combo_chart_data\" :loading=\"loading\"/>\n\n        <!-- Data Table for Combo Chart -->\n        <div v-if=\"displayChartType === 'Combo' && combo_table_data.length > 0\" class=\"data-table-section\">\n          <h4>Data Table</h4>\n          <cv-data-table\n            :columns=\"comboTableColumns\"\n            :data=\"combo_table_data\"\n            :pagination=\"{ numberOfItems: combo_table_data.length }\"\n          />\n        </div>\n\n        <!-- Comparison Charts -->\n        <div v-if=\"displayChartType === 'Compare' && compare_chart_data.length > 0\">\n          <h4>Performance Comparison</h4>\n          <LineChart :data=\"compare_chart_data\" :loading=\"loading\"/>\n\n          <!-- Comparison Summary -->\n          <div v-if=\"comparison_summary.length > 0\" class=\"comparison-summary\">\n            <h5>Comparison Summary</h5>\n            <div v-for=\"summary in comparison_summary\" :key=\"summary.partNumber\" class=\"summary-card\">\n              <cv-tile>\n                <h6>{{ summary.partNumber }}</h6>\n                <p>Total Defects: {{ summary.totalDefects }}</p>\n                <p>Top Root Cause: {{ summary.topRootCause }}</p>\n              </cv-tile>\n            </div>\n          </div>\n        </div>\n      </cv-tile>\n    </div>\n    </div>\n  </div>\n</template>\n\n\n\n<script>\nimport LineChart from '../../components/LineChart'; // Import the LineChart component\nimport BarChart from '../../components/BarChart'; //import barchart\nimport ComboChart from '../../components/LineBarComboChart'; //import combo chart\nimport MainHeader from '../../components/MainHeader';\nimport RootCauseChart from '@/components/RootCauseChart/RootCauseChart';\n\nexport default {\n  name: 'Phase1B',\n  components: {\n    LineChart,\n    BarChart,\n    ComboChart,\n    RootCauseChart,\n    MainHeader\n  },\n  data() {\n    return {\n      queryMethod: 'manual', // Default to AI query method\n      queryType: 'fabful', // Default to Fab/Ful DefRate\n      aiQuery: '',\n      selectedPN: \"\",\n      selectedArea: \"\",\n      selectedStartDate: \"\",\n      selectedEndDate: \"\",\n      selectedDateRange: \"\",\n      selectedCategory: \"\",\n      selectedChartType: \"\",\n      pnOptions: [],\n      areaOptions: [],\n      categoryOptions: [],\n      chartTypeOptions: ['Bar', 'Line'],\n      line_chart_data: [],\n      bar_chart_data: [],\n      displayChartType: \"\",\n      chartData: [], \n      lightTile:true,\n      loading: false,\n      calOptions: {dateFormat: \"Y-m-d\"},\n\n      // Root Cause Chart Data\n      rootCauseChartData: [],\n      rootCauseTitle: '',\n      isRootCauseDataLoading: true,\n\n      // Breakout Groups Data\n      breakoutGroups: [],\n\n\n      // Root Cause Chart Controls\n      rootCauseViewBy: 'rootCause',\n      rootCauseTimeRange: '6month',\n      rootCauseSelectedGroup: 'all',\n\n      // Combo DefRate Data\n      combo_chart_data: [],\n      combo_table_data: [],\n      comboDateRange: \"\",\n      selectedSuppliers: [],\n      selectedPartNumbers: [],\n      selectedVintages: [],\n      supplierOptions: [],\n      partNumberOptions: [],\n      vintageOptions: [],\n      comboTableColumns: [\n        { key: 'partNumber', header: 'Part Number' },\n        { key: 'month', header: 'Month' },\n        { key: 'defectCount', header: 'Defect Count' },\n        { key: 'volume', header: 'Volume' },\n        { key: 'failRate', header: 'Fail Rate (%)' },\n        { key: 'targetRate', header: 'Target Rate (%)' },\n        { key: 'status', header: 'Status' }\n      ],\n\n      // Compare Data\n      comparePN1: \"\",\n      comparePN2: \"\",\n      compareDateRange: \"\",\n      compare_chart_data: [],\n      comparison_summary: [],\n    };\n  },\n  mounted() {\n    this.get_PNs();\n    this.loadSuppliers();\n    this.loadVintages();\n  },\n  watch: {\n  selectedPN(newPN) {\n    if (newPN) {\n      this.get_categories();\n    }\n  },\n  pnOptions(newOptions) {\n    // Update part number options for combo functionality\n    this.partNumberOptions = newOptions.map(pn => ({\n      label: pn,\n      value: pn\n    }));\n  },\n  selectedArea(newArea) {\n    if (newArea) {\n      this.get_categories2(\"area\");\n      console.log(\"DAN1\")\n    }\n  },\n  selectedCategory(newCategory) {\n    if (newCategory) {\n      this.get_categories2(\"category\");\n    }\n  }\n},\n  methods: {\n    async processAiQuery() {\n      if (!this.aiQuery.trim()) return;\n      \n      this.loading = true;\n      \n      try {\n        const token = this.$store.getters.getToken;\n        \n        // Create a prompt for WatsonX.ai to extract parameters\n        const prompt = `\nYou are a data analysis assistant. Extract the following parameters from the user's natural language query for a manufacturing analysis system:\n\nUser Query: \"${this.aiQuery}\"\n\nPlease extract and return ONLY a JSON object with these fields:\n- pn: Part number without leading zeros (string, empty if not specified). Example: if user says \"02EA657\", return \"02EA657\"\n- area: Area/stage. Must be one of: FULL, FAB, FASM, or TEAR (string, empty if not specified)\n- category: Category/root cause like MECH, ELEC, etc. (string, empty if not specified)\n- startDate: Start date in YYYY-MM-DD format (string, empty if not specified) *for example, May 1 2024 is 2024-05-01\n- endDate: End date in YYYY-MM-DD format (string, empty if not specified) *for example, May 1 2024 is 2024-05-01\n- chartType: Either \"Bar\" or \"Line\" (string, default to \"Bar\" if not specified)\n\nExamples:\n- \"Show me bar chart for PN 02EA657 in area FULL for category MECH from 2024-06-04 to 2024-06-25\"\n  Should return: {\"pn\": \"02EA657\", \"area\": \"FULL\", \"category\": \"MECH\", \"startDate\": \"2024-06-04\", \"endDate\": \"2024-06-25\", \"chartType\": \"Bar\"}\n ***THIS IS JUST AN EXAMPLE, DO NOT USE ANY OF THESE VALUES IN THE RESPONSE***\n\n Return only the JSON object, no other text.\n`;\n\n        // Call WatsonX.ai API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"watsonx_prompt\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({\n            model_id: 'ibm/granite-13b-instruct-v2',\n            prompt: prompt,\n            temperature: 0.3,\n            api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',\n            project_id: 'edd72256-56ff-4595-bf4a-350ab686660c'\n          }),\n        });\n\n        const data = await this.handleResponse(response);\n        \n        if (data && data.status === 'success') {\n          try {\n            // Parse the AI response to extract parameters\n            const aiResponse = data.generated_text.trim();\n            console.log(\"AI Response:\", aiResponse);\n            \n            // Try to extract JSON from the response\n            let extractedParams;\n            try {\n              extractedParams = JSON.parse(aiResponse);\n            } catch (e) {\n              // If direct parsing fails, try to find JSON in the response\n              const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n              if (jsonMatch) {\n                extractedParams = JSON.parse(jsonMatch[0]);\n              } else {\n                throw new Error(\"No valid JSON found in AI response\");\n              }\n            }\n            \n            // Apply the extracted parameters with proper formatting\n            if (extractedParams.pn) {\n              // Ensure part number has proper leading zeros (10 digits total)\n              const pn = extractedParams.pn\n              this.selectedPN = pn;\n            }\n            if (extractedParams.area) this.selectedArea = extractedParams.area;\n            if (extractedParams.category) this.selectedCategory = extractedParams.category;\n            if (extractedParams.chartType) this.selectedChartType = extractedParams.chartType;\n\n            if (extractedParams.startDate && extractedParams.endDate) {\n              // Convert date format if needed (from MM-DD-YYYY to YYYY-MM-DD)\n              let startDate = extractedParams.startDate;\n              let endDate = extractedParams.endDate;\n\n              // Check if dates are in MM-DD-YYYY format and convert to YYYY-MM-DD\n              if (startDate.match(/^\\d{2}-\\d{2}-\\d{4}$/)) {\n                const [month, day, year] = startDate.split('-');\n                startDate = `${year}-${month}-${day}`;\n              }\n              if (endDate.match(/^\\d{2}-\\d{2}-\\d{4}$/)) {\n                const [month, day, year] = endDate.split('-');\n                endDate = `${year}-${month}-${day}`;\n              }\n\n              this.selectedDateRange = {\n                startDate: startDate,\n                endDate: endDate\n              };\n            }\n\n            console.log(\"Applied parameters:\", {\n              PN: this.selectedPN,\n              Area: this.selectedArea,\n              Category: this.selectedCategory,\n              ChartType: this.selectedChartType,\n              DateRange: this.selectedDateRange\n            });\n            console.log(\"This is the AIQ:,\", this.aiQuery);\n            // Automatically execute the query\n            this.handleSubmit();\n            \n            \n          } catch (error) {\n            console.error(\"Error parsing AI response:\", error);\n            alert(\"Sorry, I couldn't understand your query. Please try rephrasing it or use manual selection.\");\n          }\n        }\n      } catch (error) {\n        console.error(\"Error processing AI query:\", error);\n        alert(\"Error processing your query. Please try again.\");\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    clearAiQuery() {\n      this.aiQuery = '';\n    },\n\n    clearResults() {\n      this.bar_chart_data = [];\n      this.line_chart_data = [];\n      this.displayChartType = \"\";\n    },\n\n    transformFullDataToChartFormat(fullData) {\n      // Transform the full data format to chart format\n      if (!fullData || !Array.isArray(fullData)) {\n        return [];\n      }\n\n      return fullData.map(item => ({\n        key: item.YEAR_MONTH,\n        value: item.DEFECT_COUNT,\n        group: \"Defect Count\",\n        // Keep the full data for reference\n        fullData: item\n      }));\n    },\n\n\n        // Root Cause Chart methods\n    async loadRootCauseData() {\n      console.log('Loading root cause chart data for PQE Owner Dashboard');\n      this.isRootCauseDataLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n        this.pqeOwner = \"Albert G\";\n        // Calculate date range based on selected time range\n        const endDate = new Date();\n        const startDate = new Date();\n        const monthsToFetch = this.rootCauseTimeRange === '3month' ? 2 : 5; // 3 or 6 months including current\n        startDate.setMonth(endDate.getMonth() - monthsToFetch);\n\n        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;\n        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;\n\n        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}, Group: ${this.rootCauseSelectedGroup}`);\n\n        // Call the new API endpoint\n        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner,\n            startDate: startDateStr,\n            endDate: endDateStr,\n            selectedGroup: this.rootCauseSelectedGroup === 'all' ? null : this.rootCauseSelectedGroup,\n            viewBy: this.rootCauseViewBy\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Transform the API data to chart format\n          if (this.rootCauseViewBy === \"rootCause\") {\n            this.rootCauseTitle = 'Root Cause Analysis';\n          } else if (this.rootCauseViewBy === \"vintage\") {\n            this.rootCauseTitle = 'Vintage Code Analysis';\n          } else if (this.rootCauseViewBy === \"sector\") {\n            this.rootCauseTitle = 'Sector Analysis';\n          } else if (this.rootCauseViewBy === \"supplier\") {\n            this.rootCauseTitle = 'Supplier Code Analysis';\n          } else if (this.rootCauseViewBy === \"partNum\") {\n            this.rootCauseTitle = 'Part Number Analysis';\n          }\n          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);\n          console.log('Root cause chart data loaded:', data.categoryData);\n          console.log('Chart data length:', this.rootCauseChartData.length);\n          console.log('Sample data point:', this.rootCauseChartData[0]);\n          console.log('Breakout groups:', data.breakoutGroups);\n          console.log('Part numbers:', data.partNumbers);\n\n          console.log('Chart data loaded, watcher should handle update');\n        } else {\n          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);\n          // Fall back to mock data\n          this.rootCauseChartData = this.generateMockRootCauseData();\n          console.log('Using mock data:', this.rootCauseChartData.length);\n        }\n      } catch (error) {\n        console.error('Error loading root cause data:', error);\n        // Fall back to mock data\n        this.rootCauseChartData = this.generateMockRootCauseData();\n      } finally {\n        this.isRootCauseDataLoading = false;\n      }\n    },\n\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // or navigating to the MetisXFactors Group tab with this specific category\n        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\\nFail Rate: ${clickedData.value}%`);\n      }\n    },\n\n    transformRootCauseData(categoryData) {\n      // Transform the API response into chart format\n      const chartData = [];\n\n      // categoryData structure: { \"category\": { \"2024-03\": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }\n      for (const [category, monthData] of Object.entries(categoryData)) {\n        // Clean up category name (trim whitespace)\n        const cleanCategory = category.trim();\n        console.log(\"trim cat\", cleanCategory)\n\n        for (const [month, data] of Object.entries(monthData)) {\n          chartData.push({\n            group: cleanCategory,\n            key: month,\n            value: parseFloat(data.failRate.toFixed(2))\n          });\n        }\n      }\n\n      console.log('Transformed root cause data:', chartData);\n      console.log('Unique groups in data:', [...new Set(chartData.map(d => d.group))]);\n      console.log('Unique keys in data:', [...new Set(chartData.map(d => d.key))]);\n      console.log('Value range:', Math.min(...chartData.map(d => d.value)), 'to', Math.max(...chartData.map(d => d.value)));\n      return chartData;\n    },\n\n    generateMockRootCauseData() {\n      // Use the actual breakout groups for this PQE owner instead of generic categories\n      let categories = [];\n\n      if (this.breakoutGroups && this.breakoutGroups.length > 0) {\n        // Use the actual breakout groups for this PQE owner\n        categories = this.breakoutGroups.map(group => group.name);\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      } else {\n        // Fallback to sample data if breakout groups aren't loaded yet\n        if (this.pqeOwner === 'Albert G.') {\n          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];\n        } else if (this.pqeOwner === 'Sarah L.') {\n          categories = ['Stellar Core', 'Nebula Drive'];\n        } else {\n          categories = ['Sample Group 1', 'Sample Group 2'];\n        }\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      }\n\n      // Generate months based on selected time range\n      const now = new Date();\n      const months = [];\n      const monthsToGenerate = this.rootCauseTimeRange === '3month' ? 3 : 6;\n\n      for (let i = monthsToGenerate - 1; i >= 0; i--) {\n        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        months.push(`${year}-${month}`);\n      }\n\n      const data = [];\n\n      months.forEach(month => {\n        categories.forEach(category => {\n          // Generate realistic fail rate data (0-5% range)\n          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%\n          const variation = (Math.random() - 0.5) * 1; // ±0.5%\n          const failRate = Math.max(0.1, baseRate + variation);\n\n          data.push({\n            group: category,\n            key: month,\n            value: parseFloat(failRate.toFixed(2))\n          });\n        });\n      });\n\n      return data;\n    },\n\n    async load_line_chart() {\n      try {\n\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_fail_count_by_category_db\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({ PN: this.selectedPN, category: this.selectedCategory, area: this.selectedArea }),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          // Use full_data if available (Phase1B format), otherwise use counts_by_period (legacy format)\n          if (data.full_data && data.full_data.length > 0) {\n            console.log(\"Using full data format:\", data.full_data);\n            // Transform full data to chart format\n            this.line_chart_data = this.transformFullDataToChartFormat(data.full_data);\n          } else {\n            console.log(\"Using legacy counts_by_period format:\", data.counts_by_period);\n            this.line_chart_data = data.counts_by_period;\n          }\n          console.log(\"Line chart data:\", this.line_chart_data);\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }finally {\n        this.loading = false;  // Set loading to false once data is loaded\n      }\n    },\n\n    async load_bar_chart() {\n      try {\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_fail_count_by_category_db\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({PN: this.selectedPN, category: this.selectedCategory, area: this.selectedArea, startDate: this.selectedStartDate, endDate: this.selectedEndDate}),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          // Use full_data if available (Phase1B format), otherwise use counts_by_period (legacy format)\n          if (data.full_data && data.full_data.length > 0) {\n            console.log(\"Using full data format for bar chart:\", data.full_data);\n            // Transform full data to chart format\n            const transformedData = this.transformFullDataToChartFormat(data.full_data);\n            this.bar_chart_data = transformedData;\n            this.line_chart_data = transformedData;\n          } else {\n            console.log(\"Using legacy counts_by_period format for bar chart:\", data.counts_by_period);\n            this.bar_chart_data = data.counts_by_period;\n            this.line_chart_data = data.counts_by_period;\n          }\n          console.log(\"Bar chart data:\", this.bar_chart_data);\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }finally {\n        this.loading = false;  // Set loading to false once data is loaded\n      }\n    },\n\n    async get_categories() {\n      try {\n        let user_type = this.$store.getters.getUser_type;\n        let action = \"view\";\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unique_categories_db\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({ \"PN\": this.selectedPN, user_type, action }),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          this.categoryOptions = data.all_categories;\n          this.areaOptions = data.all_stages;\n          console.log(this.areaOptions)\n          console.log(\"Received data:\", data);\n\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }\n    },\n\n    async get_PNs() {\n      try {\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unique_pns\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({}),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          this.pnOptions = data.all_pns;\n\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }\n    },\n\n    async get_categories2(changedVar) {\n      try {\n        let user_type = this.$store.getters.getUser_type;\n        let action = \"view\";\n        let area = null;\n        let category = null;\n\n        if (changedVar === \"area\"){\n          area = this.selectedArea\n        }else if (changedVar === \"category\"){\n          category = this.selectedCategory\n        }\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unique_categories_db2\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({ \"PN\": this.selectedPN, \"area\": area, \"category\": category, user_type, action }),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          if (changedVar === \"area\"){\n            this.categoryOptions = data.new_array;\n          } else if (changedVar === \"category\"){\n            this.areaOptions = data.new_array;\n          }\n\n          console.log(\"Received data:\", data);\n\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }\n    },\n\n    handleRootCauseViewByChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseTimeRangeChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseGroupChange() {\n      console.log(`Root cause group changed to: ${this.rootCauseSelectedGroup}`);\n      console.log('Current chart data length before reload:', this.rootCauseChartData.length);\n      this.loadRootCauseData();\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    },\n\n    handleResponse(response) {\n      if (!response.ok) {\n        if (response.status === 401) {\n          this.session_expired_visible = true;\n        }\n      } else {\n        return response.json();\n      }\n    },\n\n    handleBarClick(data) {\n      // Update the chart data to only show the clicked bar\n      console.log(\"Bar data received:\", data);\n      // this.bar_chart_data = [data];\n    },\n\n    handlePointClick(data) {\n      console.log(\"Point clicked:\", data);\n      // Navigate to a new page or display a message\n      this.$router.push({ name: 'HelloPage' });\n    },\n\n    handleQueryMethodChange(newVal) {\n      // console.log(\"Query method changed:\", newVal);\n      this.queryMethod = newVal;\n      this.clearResults();\n    },\n\n    handleQueryTypeChange(newVal) {\n      // console.log(\"Query method changed:\", newVal);\n      this.queryType = newVal;\n      this.clearResults();\n    },\n\n    handleSubmit() {\n      this.loading = true;\n      this.aiQuery = '';\n      this.bar_chart_data = [];\n      this.line_chart_data = [];\n      console.log('PN:', this.selectedPN);\n      console.log('Area:', this.selectedArea);\n      console.log('Category:', this.selectedCategory);\n      console.log('Chart Type:', this.selectedChartType);\n      console.log(`Date Range: Start: ${this.selectedDate}`, );\n      this.selectedStartDate = this.selectedDateRange.startDate;\n      this.selectedEndDate = this.selectedDateRange.endDate;\n      // Update chart data based on selected chart type\n      if (this.selectedChartType === 'Bar') {\n        //this.chartData = this.bar_chart_data;\n        this.displayChartType = 'Bar';\n        this.load_bar_chart()\n      } else if (this.selectedChartType === 'Line') {\n        // this.chartData = this.line_chart_data;\n        this.displayChartType = 'Line';\n        this.load_line_chart()\n      }\n    },\n\n    async loadSuppliers() {\n      try {\n        const token = this.$store.getters.getToken;\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_suppliers\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({}),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data.status_res === \"success\") {\n          this.supplierOptions = data.suppliers.map(supplier => ({\n            label: supplier,\n            value: supplier\n          }));\n        }\n      } catch (error) {\n        console.error(\"Error loading suppliers:\", error);\n      }\n    },\n\n    async loadVintages() {\n      try {\n        const token = this.$store.getters.getToken;\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_vintages\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({}),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data.status_res === \"success\") {\n          this.vintageOptions = data.vintages.map(vintage => ({\n            label: vintage,\n            value: vintage\n          }));\n        }\n      } catch (error) {\n        console.error(\"Error loading vintages:\", error);\n      }\n    },\n\n    async handleComboSubmit() {\n      this.loading = true;\n      this.displayChartType = 'Combo';\n      this.combo_chart_data = [];\n      this.combo_table_data = [];\n\n      try {\n        const token = this.$store.getters.getToken;\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_combo_defrate_data\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({\n            startDate: this.comboDateRange.startDate,\n            endDate: this.comboDateRange.endDate,\n            suppliers: this.selectedSuppliers.map(s => s.value),\n            partNumbers: this.selectedPartNumbers.map(pn => pn.value),\n            vintages: this.selectedVintages.map(v => v.value)\n          }),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data.status_res === \"success\") {\n          this.combo_chart_data = data.chart_data;\n          this.combo_table_data = data.table_data;\n        }\n      } catch (error) {\n        console.error(\"Error loading combo data:\", error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async handleCompareSubmit() {\n      if (!this.comparePN1 || !this.comparePN2) {\n        alert(\"Please select both part numbers for comparison\");\n        return;\n      }\n\n      this.loading = true;\n      this.displayChartType = 'Compare';\n      this.compare_chart_data = [];\n      this.comparison_summary = [];\n\n      try {\n        const token = this.$store.getters.getToken;\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_compare_data\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({\n            partNumber1: this.comparePN1,\n            partNumber2: this.comparePN2,\n            startDate: this.compareDateRange.startDate,\n            endDate: this.compareDateRange.endDate\n          }),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data.status_res === \"success\") {\n          this.compare_chart_data = data.chart_data;\n\n          // Process comparison summary\n          this.comparison_summary = data.comparison_data.map(item => ({\n            partNumber: item.partNumber,\n            totalDefects: item.totalDefects,\n            topRootCause: Object.keys(item.rootCauses).reduce((a, b) =>\n              item.rootCauses[a] > item.rootCauses[b] ? a : b\n            )\n          }));\n        }\n      } catch (error) {\n        console.error(\"Error loading comparison data:\", error);\n      } finally {\n        this.loading = false;\n      }\n    },\n  }\n};\n</script>\n"]}]}