{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1B\\Phase1B.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1B\\Phase1B.vue", "mtime": 1757108602483}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Phase1B.vue"], "names": [], "mappings": ";AAsk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file": "Phase1B.vue", "sourceRoot": "src/views/Phase1B", "sourcesContent": ["<style scoped>\n.short-dropdown {\n  max-width: 200px; /* Adjust the width as needed */\n}\n\n.submit-button {\n  margin-top: 20px; /* Add some spacing above the button */\n}\n\n.flex-container {\n  display: flex;\n  align-items: flex-start;\n  gap: 24px;\n  padding: 16px;\n  min-height: calc(100vh - 120px);\n}\n\n.left-column {\n  flex: 0 0 350px;\n  /* background-color: #f4f4f4; */\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.right-column {\n  flex: 1;\n  min-width: 0;\n}\n\n.ai-query-section {\n  margin-bottom: 20px;\n  padding: 15px;\n \n  /* background-color: #f4f4f400;  */\n}\n\n/* .ai-query-label {\n  font-weight: 600;\n  margin-bottom: 10px;\n  color: #e3e3e3;\n} */\n\n.ai-textarea {\n  width: 100%;\n  margin-bottom: 15px;\n}\n\n.ai-buttons {\n  display: flex;\n  gap: 10px;\n}\n\n.query-method-toggle {\n  margin-bottom: 24px;\n  padding: 16px;\n\n  border-radius: 6px;\n\n}\n\n.manual-controls {\n  margin-top: 20px;\n  padding: 16px;\n\n  border-radius: 6px;\n\n}\n\n.combo-controls {\n  margin-top: 20px;\n  padding: 20px;\n\n  border-radius: 6px;\n \n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.combo-controls h3 {\n  margin-bottom: 20px;\n  color: #161616;\n  font-weight: 600;\n  font-size: 1.125rem;\n}\n\n.compare-controls {\n  margin-top: 20px;\n  padding: 20px;\n \n  border-radius: 6px;\n\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.compare-controls h3 {\n  margin-bottom: 20px;\n  color: #161616;\n  font-weight: 600;\n  font-size: 1.125rem;\n}\n\n.data-table-section {\n  margin-top: 20px;\n  padding: 15px;\n  border-radius: 4px;\n}\n\n.comparison-summary {\n  margin-top: 20px;\n}\n\n.summary-card {\n  margin-bottom: 10px;\n}\n\n.summary-card h6 {\n  margin-bottom: 5px;\n  font-weight: 600;\n}\n\n.phase1b-container {\n  /* background-color: #f4f4f4; */\n  min-height: 100vh;\n}\n\n.left-column h1 {\n  color: #161616;\n  font-size: 1.5rem;\n  font-weight: 600;\n  margin-bottom: 24px;\n  border-bottom: 2px solid #0f62fe;\n  padding-bottom: 8px;\n}\n\n.short-dropdown {\n  margin-bottom: 16px;\n}\n\n.submit-button {\n  margin-top: 20px;\n  width: 100%;\n}\n\n.ai-query-section {\n  margin-bottom: 24px;\n  padding: 20px;\n  background-color: #ffffff;\n  border-radius: 6px;\n  border: 1px solid #e0e0e0;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.ai-query-label {\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: #161616;\n  font-size: 1rem;\n}\n\n.chart-controls {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.control-group {\n  display: flex;\n  flex-direction: column;\n}\n\n.control-label {\n  font-weight: 500;\n  margin-bottom: 8px;\n  color: #525252;\n}\n\n.control-dropdown {\n  width: 100%;\n}\n\n.dynamic-filters {\n  margin-top: 16px;\n  padding: 16px;\n  /* background-color: #f9f9f9; */\n  border-radius: 4px;\n  border: 1px dashed #c6c6c6;\n}\n\n.dynamic-filters .short-dropdown {\n  margin-bottom: 12px;\n}\n</style>\n\n<template>\n  \n  <div class=\"phase1b-container\">\n    <MainHeader :expandedSideNav=false :useFixed=\"useFixed\" />\n    <div class=\"flex-container\">\n      <div class=\"left-column\">\n      <h1 style=\"color: #ffffff;\">Phase 1</h1>\n\n      <!-- Query Method Toggle -->\n      <div class=\"query-method-toggle\">\n        <cv-radio-group\n          v-model=\"queryMethod\"\n          @change=\"handleQueryMethodChange\"\n          label=\"Query Method\"\n          vertical\n        >\n          <cv-radio-button\n            name=\"queryMethod\"\n            label=\"AI Query\"\n            value=\"ai\"\n          />\n          <cv-radio-button\n            name=\"queryMethod\"\n            label=\"Dropdown Selection\"\n            value=\"manual\"\n          />\n        </cv-radio-group>\n      </div>\n\n      <!-- AI Query Section -->\n      <div v-if=\"queryMethod === 'ai'\" >\n        <div style=\"color: #ffffff;\">What would you like to view?</div>\n        <cv-text-area\n          v-model=\"aiQuery\"\n          placeholder=\"Example: Show me bar chart for PN ABC123 in area XYZ for category defects from 2024-01-01 to 2024-03-31\"\n          rows=\"4\"\n          class=\"ai-textarea\"\n        />\n        <div class=\"ai-buttons\">\n          <cv-button \n            @click=\"processAiQuery\" \n            :disabled=\"loading || !aiQuery.trim()\"\n            kind=\"primary\"\n          >\n            {{ loading ? 'Processing...' : 'Generate Query' }}\n          </cv-button>\n          <cv-button \n            @click=\"clearAiQuery\" \n            kind=\"secondary\"\n          >\n            Clear\n          </cv-button>\n        </div>\n      </div>\n\n      <!-- Manual Controls Section -->\n      <div v-if=\"queryMethod === 'manual'\" class=\"manual-controls\">\n        <cv-radio-group\n          v-model=\"queryType\"\n          @change=\"handleQueryTypeChange\"\n          label=\"Query Type\"\n          vertical\n        >\n          <cv-radio-button\n            name=\"queryType\"\n            label=\"Fab/Ful DefRate\"\n            value=\"fabful\"\n          />\n          <cv-radio-button\n            name=\"queryType\"\n            label=\"Pareto\"\n            value=\"pareto\"\n          />\n          <cv-radio-button\n            name=\"queryType\"\n            label=\"Combo DefRate\"\n            value=\"combo\"\n          />\n          <cv-radio-button\n            name=\"queryType\"\n            label=\"Compare\"\n            value=\"compare\"\n          />\n        </cv-radio-group>\n        <div v-if=\"queryType === 'fabful'\" >\n              \n\n              <div class=\"chart-controls\">\n                <div class=\"control-group\">\n                  <label class=\"control-label\">View By:</label>\n                  <cv-dropdown\n                    v-model=\"rootCauseViewBy\"\n                    @change=\"handleRootCauseViewByChange\"\n                    class=\"control-dropdown\"\n                  >\n                    <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n                    <cv-dropdown-item value=\"vintage\">Vintage</cv-dropdown-item>\n                    <cv-dropdown-item value=\"sector\">Sector</cv-dropdown-item>\n                    <cv-dropdown-item value=\"supplier\">Supplier</cv-dropdown-item>\n                    <cv-dropdown-item value=\"partNum\">Part Number</cv-dropdown-item>\n                  </cv-dropdown>\n                </div>\n\n                <div class=\"control-group\">\n                  <label class=\"control-label\">Time Range:</label>\n                  <cv-dropdown\n                    v-model=\"rootCauseTimeRange\"\n                    @change=\"handleRootCauseTimeRangeChange\"\n                    class=\"control-dropdown\"\n                  >\n                    <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n                    <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n                  </cv-dropdown>\n                </div>\n\n                <div class=\"control-group\" v-if=\"breakoutGroups.length > 0\">\n                  <label class=\"control-label\">Group:</label>\n                  <cv-dropdown\n                    v-model=\"rootCauseSelectedGroup\"\n                    @change=\"handleRootCauseGroupChange\"\n                    class=\"control-dropdown\"\n                  >\n                    <cv-dropdown-item value=\"all\">All Groups</cv-dropdown-item>\n                    <cv-dropdown-item\n                      v-for=\"group in breakoutGroups\"\n                      :key=\"group.name\"\n                      :value=\"group.name\"\n                    >\n                      {{ group.name }}\n                    </cv-dropdown-item>\n                  </cv-dropdown>\n                </div>\n              </div>\n\n              <div class=\"chart-container\">\n                <div v-if=\"isRootCauseDataLoading\" >\n                          Loading Chart...\n                          <RootCauseChart :data = [] :loading=\"isRootCauseDataLoading\"/>\n                        </div>\n                <RootCauseChart\n                 v-if=\"rootCauseChartData.length > 0 && !isRootCauseDataLoading\"\n                  :data=\"rootCauseChartData\"\n                  :viewBy=\"rootCauseViewBy\"\n                  :timeRange=\"rootCauseTimeRange\"\n                  :selectedGroup=\"rootCauseSelectedGroup\"\n                  :loading=\"isRootCauseDataLoading\"\n                  :title = \"rootCauseTitle\"\n                  @bar-click=\"handleRootCauseBarClick\"\n                />\n\n                <div v-if=\"rootCauseChartData.length == 0 && !isRootCauseDataLoading\" >\n                          No data available\n                </div>\n              </div>\n            \n              \n      </div>\n        <div v-if=\"queryType === 'combo'\" >\n          <!-- Combo DefRate Controls -->\n          <div class=\"combo-controls\">\n            <h3>Combo DefRate Analysis</h3>\n\n            <!-- Start Date -->\n            <cv-date-picker\n              v-model=\"comboStartDate\"\n              label=\"Start Date\"\n              :cal-options=\"calOptions\"\n              placeholder=\"yyyy-mm-dd\"\n              class=\"short-dropdown\"\n            />\n\n            <!-- End Date -->\n            <cv-date-picker\n              v-model=\"comboEndDate\"\n              label=\"End Date\"\n              :cal-options=\"calOptions\"\n              placeholder=\"yyyy-mm-dd\"\n              class=\"short-dropdown\"\n            />\n\n            <!-- View Multi-Select -->\n            <cv-multi-select\n              v-model=\"selectedViews\"\n              label=\"View By\"\n              :options=\"viewOptions\"\n              class=\"short-dropdown\"\n              @change=\"handleViewChange\"\n            />\n\n            <!-- Dynamic Filter based on View Selection -->\n            <div v-if=\"selectedViews.length > 0\" class=\"dynamic-filters\">\n\n              <!-- Group Filter -->\n              <cv-multi-select\n                v-if=\"selectedViews.some(v => v.value === 'group')\"\n                v-model=\"selectedGroups\"\n                label=\"Groups\"\n                :options=\"groupOptions\"\n                class=\"short-dropdown\"\n              />\n\n              <!-- Part Number Filter -->\n              <cv-multi-select\n                v-if=\"selectedViews.some(v => v.value === 'partNumber')\"\n                v-model=\"selectedPartNumbers\"\n                label=\"Part Numbers\"\n                :options=\"partNumberOptions\"\n                class=\"short-dropdown\"\n              />\n\n              <!-- Sector Filter -->\n              <cv-multi-select\n                v-if=\"selectedViews.some(v => v.value === 'sector')\"\n                v-model=\"selectedSectors\"\n                label=\"Sectors\"\n                :options=\"sectorOptions\"\n                class=\"short-dropdown\"\n              />\n\n              <!-- Vintage Filter -->\n              <cv-multi-select\n                v-if=\"selectedViews.some(v => v.value === 'vintage')\"\n                v-model=\"selectedVintages\"\n                label=\"Vintages\"\n                :options=\"vintageOptions\"\n                class=\"short-dropdown\"\n              />\n\n              <!-- Supplier Filter -->\n              <cv-multi-select\n                v-if=\"selectedViews.some(v => v.value === 'supplier')\"\n                v-model=\"selectedSuppliers\"\n                label=\"Suppliers\"\n                :options=\"supplierOptions\"\n                class=\"short-dropdown\"\n              />\n\n            </div>\n\n            <!-- Submit Button -->\n            <cv-button @click=\"handleComboSubmit\" class=\"submit-button\" kind=\"primary\">\n              Generate Combo Chart\n            </cv-button>\n          </div>\n        </div>\n\n        <div v-if=\"queryType === 'pareto'\" >\n        \n        \n        <!-- PN Dropdown -->\n        <cv-combo-box class=\"short-dropdown\"\n          v-model=\"selectedPN\"\n          label=\"PN\"\n          :options=\"pnOptions\"\n        ></cv-combo-box>\n\n        <!-- Area Dropdown -->\n        <cv-dropdown class=\"short-dropdown\"\n          v-model=\"selectedArea\"\n          label=\"Area\"\n          :items=\"areaOptions\"\n        ></cv-dropdown>\n\n        <!-- Category Dropdown -->\n        <cv-dropdown class=\"short-dropdown\"\n          v-model=\"selectedCategory\"\n          label=\"Category\"\n          :items=\"categoryOptions\"\n        ></cv-dropdown>\n\n         <!-- Date Select -->\n         <cv-date-picker\n          v-model=\"selectedDateRange\"\n          label=\"Choose Date Range\"\n          kind = 'range'\n          :cal-options = \"calOptions\"\n          placeholder = \"yyyy-mm-dd\"\n        ></cv-date-picker>\n\n        <!-- Chart Type Dropdown -->\n        <cv-dropdown class=\"short-dropdown\"\n          v-model=\"selectedChartType\"\n          label=\"Chart Type\"\n          :items=\"chartTypeOptions\"\n        ></cv-dropdown>\n\n        <!-- Submit Button -->\n        <cv-button @click=\"handleSubmit\" class=\"submit-button\">\n          Submit\n        </cv-button>\n      </div>\n\n      <div v-if=\"queryType === 'compare'\" >\n        <!-- Compare Section -->\n        <div class=\"compare-controls\">\n          <h3>Compare Part Numbers</h3>\n\n          <!-- Part Number 1 -->\n          <cv-combo-box\n            v-model=\"comparePN1\"\n            label=\"Part Number 1\"\n            :options=\"pnOptions\"\n            class=\"short-dropdown\"\n          />\n\n          <!-- Part Number 2 -->\n          <cv-combo-box\n            v-model=\"comparePN2\"\n            label=\"Part Number 2\"\n            :options=\"pnOptions\"\n            class=\"short-dropdown\"\n          />\n\n          <!-- Date Range for Comparison -->\n          <cv-date-picker\n            v-model=\"compareDateRange\"\n            label=\"Comparison Date Range\"\n            kind=\"range\"\n            :cal-options=\"calOptions\"\n            placeholder=\"yyyy-mm-dd\"\n          />\n\n          <!-- Submit Button -->\n          <cv-button @click=\"handleCompareSubmit\" class=\"submit-button\" kind=\"primary\">\n            Compare Performance\n          </cv-button>\n        </div>\n      </div>\n      </div>\n\n    </div>\n\n    <div class=\"right-column\">\n      <cv-tile :light=\"lightTile\">\n        <!-- Show loading skeletons while data is being fetched -->\n        <div v-if=\"loading && displayChartType === 'Bar'\">\n          Loading Bar Chart...\n          <BarChart :data = [] :loading=\"loading\"/>\n        </div>\n        <div v-if=\"loading && displayChartType === 'Line'\" >\n          Loading Line Chart...\n          <LineChart :data = [] :loading=\"loading\"/>\n        </div>\n        <div v-if=\"loading && displayChartType === 'Combo'\" >\n          Loading Combo Chart...\n          <ComboChart :data = [] :loading=\"loading\"/>\n        </div>\n\n        <!-- Show charts after data is loaded -->\n        <BarChart v-if=\"displayChartType === 'Bar' && bar_chart_data.length > 0\" :data=\"bar_chart_data\" @bar-clicked=\"handleBarClick\" :loading=\"loading\"/>\n        <LineChart v-if=\"displayChartType === 'Line' && line_chart_data.length > 0\" :data=\"line_chart_data\" @point-clicked=\"handlePointClick\" :loading=\"loading\"/>\n        <ComboChart v-if=\"displayChartType === 'Combo' && combo_chart_data.length > 0\" :data=\"combo_chart_data\" :loading=\"loading\"/>\n\n        <!-- Data Table for Combo Chart -->\n        <div v-if=\"displayChartType === 'Combo' && combo_table_data.length > 0\" class=\"data-table-section\">\n          <h4>Data Table</h4>\n          <cv-data-table\n            :columns=\"comboTableColumns\"\n            :data=\"combo_table_data\"\n            :pagination=\"{ numberOfItems: combo_table_data.length }\"\n          />\n        </div>\n\n        <!-- Comparison Charts -->\n        <div v-if=\"displayChartType === 'Compare' && compare_chart_data.length > 0\">\n          <h4>Performance Comparison</h4>\n          <LineChart :data=\"compare_chart_data\" :loading=\"loading\"/>\n\n          <!-- Comparison Summary -->\n          <div v-if=\"comparison_summary.length > 0\" class=\"comparison-summary\">\n            <h5>Comparison Summary</h5>\n            <div v-for=\"summary in comparison_summary\" :key=\"summary.partNumber\" class=\"summary-card\">\n              <cv-tile>\n                <h6>{{ summary.partNumber }}</h6>\n                <p>Total Defects: {{ summary.totalDefects }}</p>\n                <p>Top Root Cause: {{ summary.topRootCause }}</p>\n              </cv-tile>\n            </div>\n          </div>\n        </div>\n      </cv-tile>\n    </div>\n    </div>\n  </div>\n</template>\n\n\n\n<script>\nimport LineChart from '../../components/LineChart'; // Import the LineChart component\nimport BarChart from '../../components/BarChart'; //import barchart\nimport ComboChart from '../../components/LineBarComboChart'; //import combo chart\nimport MainHeader from '../../components/MainHeader';\nimport RootCauseChart from '@/components/RootCauseChart/RootCauseChart';\n\nexport default {\n  name: 'Phase1B',\n  components: {\n    LineChart,\n    BarChart,\n    ComboChart,\n    RootCauseChart,\n    MainHeader\n  },\n  data() {\n    return {\n      queryMethod: 'manual', // Default to AI query method\n      queryType: 'fabful', // Default to Fab/Ful DefRate\n      aiQuery: '',\n      selectedPN: \"\",\n      selectedArea: \"\",\n      selectedStartDate: \"\",\n      selectedEndDate: \"\",\n      selectedDateRange: \"\",\n      selectedCategory: \"\",\n      selectedChartType: \"\",\n      pnOptions: [],\n      areaOptions: [],\n      categoryOptions: [],\n      chartTypeOptions: ['Bar', 'Line'],\n      line_chart_data: [],\n      bar_chart_data: [],\n      displayChartType: \"\",\n      chartData: [], \n      lightTile:true,\n      loading: false,\n      calOptions: {dateFormat: \"Y-m-d\"},\n\n      // Root Cause Chart Data\n      rootCauseChartData: [],\n      rootCauseTitle: '',\n      isRootCauseDataLoading: true,\n\n      // Breakout Groups Data\n      breakoutGroups: [],\n\n\n      // Root Cause Chart Controls\n      rootCauseViewBy: 'rootCause',\n      rootCauseTimeRange: '6month',\n      rootCauseSelectedGroup: 'all',\n\n      // Combo DefRate Data\n      combo_chart_data: [],\n      combo_table_data: [],\n      comboStartDate: \"\",\n      comboEndDate: \"\",\n      selectedViews: [],\n      selectedGroups: [],\n      selectedPartNumbers: [],\n      selectedSectors: [],\n      selectedVintages: [],\n      selectedSuppliers: [],\n\n      // Options for dropdowns\n      viewOptions: [\n        { label: 'Group', value: 'group' },\n        { label: 'Part Number', value: 'partNumber' },\n        { label: 'Sector', value: 'sector' },\n        { label: 'Vintage', value: 'vintage' },\n        { label: 'Supplier', value: 'supplier' }\n      ],\n      groupOptions: [],\n      partNumberOptions: [],\n      sectorOptions: [],\n      vintageOptions: [],\n      supplierOptions: [],\n      comboTableColumns: [\n        { key: 'view', header: 'View' },\n        { key: 'category', header: 'Category' },\n        { key: 'month', header: 'Month' },\n        { key: 'defectCount', header: 'Defect Count' },\n        { key: 'volume', header: 'Volume' },\n        { key: 'failRate', header: 'Fail Rate (%)' },\n        { key: 'targetRate', header: 'Target Rate (%)' },\n        { key: 'status', header: 'Status' }\n      ],\n\n      // Compare Data\n      comparePN1: \"\",\n      comparePN2: \"\",\n      compareDateRange: \"\",\n      compare_chart_data: [],\n      comparison_summary: [],\n    };\n  },\n  mounted() {\n    this.get_PNs();\n    this.loadSuppliers();\n    this.loadVintages();\n    this.loadGroups();\n    this.loadSectors();\n  },\n  watch: {\n  selectedPN(newPN) {\n    if (newPN) {\n      this.get_categories();\n    }\n  },\n  pnOptions(newOptions) {\n    // Update part number options for combo functionality\n    this.partNumberOptions = newOptions.map(pn => ({\n      label: pn,\n      value: pn\n    }));\n  },\n  selectedArea(newArea) {\n    if (newArea) {\n      this.get_categories2(\"area\");\n    }\n  },\n  selectedCategory(newCategory) {\n    if (newCategory) {\n      this.get_categories2(\"category\");\n    }\n  }\n},\n  methods: {\n    async processAiQuery() {\n      if (!this.aiQuery.trim()) return;\n      \n      this.loading = true;\n      \n      try {\n        const token = this.$store.getters.getToken;\n        \n        // Create a prompt for WatsonX.ai to extract parameters\n        const prompt = `\nYou are a data analysis assistant. Extract the following parameters from the user's natural language query for a manufacturing analysis system:\n\nUser Query: \"${this.aiQuery}\"\n\nPlease extract and return ONLY a JSON object with these fields:\n- pn: Part number without leading zeros (string, empty if not specified). Example: if user says \"02EA657\", return \"02EA657\"\n- area: Area/stage. Must be one of: FULL, FAB, FASM, or TEAR (string, empty if not specified)\n- category: Category/root cause like MECH, ELEC, etc. (string, empty if not specified)\n- startDate: Start date in YYYY-MM-DD format (string, empty if not specified) *for example, May 1 2024 is 2024-05-01\n- endDate: End date in YYYY-MM-DD format (string, empty if not specified) *for example, May 1 2024 is 2024-05-01\n- chartType: Either \"Bar\" or \"Line\" (string, default to \"Bar\" if not specified)\n\nExamples:\n- \"Show me bar chart for PN 02EA657 in area FULL for category MECH from 2024-06-04 to 2024-06-25\"\n  Should return: {\"pn\": \"02EA657\", \"area\": \"FULL\", \"category\": \"MECH\", \"startDate\": \"2024-06-04\", \"endDate\": \"2024-06-25\", \"chartType\": \"Bar\"}\n ***THIS IS JUST AN EXAMPLE, DO NOT USE ANY OF THESE VALUES IN THE RESPONSE***\n\n Return only the JSON object, no other text.\n`;\n\n        // Call WatsonX.ai API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"watsonx_prompt\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({\n            model_id: 'ibm/granite-13b-instruct-v2',\n            prompt: prompt,\n            temperature: 0.3,\n            api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',\n            project_id: 'edd72256-56ff-4595-bf4a-350ab686660c'\n          }),\n        });\n\n        const data = await this.handleResponse(response);\n        \n        if (data && data.status === 'success') {\n          try {\n            // Parse the AI response to extract parameters\n            const aiResponse = data.generated_text.trim();\n            console.log(\"AI Response:\", aiResponse);\n            \n            // Try to extract JSON from the response\n            let extractedParams;\n            try {\n              extractedParams = JSON.parse(aiResponse);\n            } catch (e) {\n              // If direct parsing fails, try to find JSON in the response\n              const jsonMatch = aiResponse.match(/\\{[\\s\\S]*\\}/);\n              if (jsonMatch) {\n                extractedParams = JSON.parse(jsonMatch[0]);\n              } else {\n                throw new Error(\"No valid JSON found in AI response\");\n              }\n            }\n            \n            // Apply the extracted parameters with proper formatting\n            if (extractedParams.pn) {\n              // Ensure part number has proper leading zeros (10 digits total)\n              const pn = extractedParams.pn\n              this.selectedPN = pn;\n            }\n            if (extractedParams.area) this.selectedArea = extractedParams.area;\n            if (extractedParams.category) this.selectedCategory = extractedParams.category;\n            if (extractedParams.chartType) this.selectedChartType = extractedParams.chartType;\n\n            if (extractedParams.startDate && extractedParams.endDate) {\n              // Convert date format if needed (from MM-DD-YYYY to YYYY-MM-DD)\n              let startDate = extractedParams.startDate;\n              let endDate = extractedParams.endDate;\n\n              // Check if dates are in MM-DD-YYYY format and convert to YYYY-MM-DD\n              if (startDate.match(/^\\d{2}-\\d{2}-\\d{4}$/)) {\n                const [month, day, year] = startDate.split('-');\n                startDate = `${year}-${month}-${day}`;\n              }\n              if (endDate.match(/^\\d{2}-\\d{2}-\\d{4}$/)) {\n                const [month, day, year] = endDate.split('-');\n                endDate = `${year}-${month}-${day}`;\n              }\n\n              this.selectedDateRange = {\n                startDate: startDate,\n                endDate: endDate\n              };\n            }\n\n            console.log(\"Applied parameters:\", {\n              PN: this.selectedPN,\n              Area: this.selectedArea,\n              Category: this.selectedCategory,\n              ChartType: this.selectedChartType,\n              DateRange: this.selectedDateRange\n            });\n            console.log(\"This is the AIQ:,\", this.aiQuery);\n            // Automatically execute the query\n            this.handleSubmit();\n            \n            \n          } catch (error) {\n            console.error(\"Error parsing AI response:\", error);\n            alert(\"Sorry, I couldn't understand your query. Please try rephrasing it or use manual selection.\");\n          }\n        }\n      } catch (error) {\n        console.error(\"Error processing AI query:\", error);\n        alert(\"Error processing your query. Please try again.\");\n      } finally {\n        this.loading = false;\n      }\n    },\n    \n    clearAiQuery() {\n      this.aiQuery = '';\n    },\n\n    clearResults() {\n      this.bar_chart_data = [];\n      this.line_chart_data = [];\n      this.displayChartType = \"\";\n    },\n\n    transformFullDataToChartFormat(fullData) {\n      // Transform the full data format to chart format\n      if (!fullData || !Array.isArray(fullData)) {\n        return [];\n      }\n\n      return fullData.map(item => ({\n        key: item.YEAR_MONTH,\n        value: item.DEFECT_COUNT,\n        group: \"Defect Count\",\n        // Keep the full data for reference\n        fullData: item\n      }));\n    },\n\n\n        // Root Cause Chart methods\n    async loadRootCauseData() {\n      console.log('Loading root cause chart data for PQE Owner Dashboard');\n      this.isRootCauseDataLoading = true;\n\n      try {\n        // Get authentication config\n        const config = this.getAuthConfig();\n        this.pqeOwner = \"Albert G\";\n        // Calculate date range based on selected time range\n        const endDate = new Date();\n        const startDate = new Date();\n        const monthsToFetch = this.rootCauseTimeRange === '3month' ? 2 : 5; // 3 or 6 months including current\n        startDate.setMonth(endDate.getMonth() - monthsToFetch);\n\n        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;\n        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;\n\n        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}, Group: ${this.rootCauseSelectedGroup}`);\n\n        // Call the new API endpoint\n        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            ...config.headers\n          },\n          body: JSON.stringify({\n            pqeOwner: this.pqeOwner,\n            startDate: startDateStr,\n            endDate: endDateStr,\n            selectedGroup: this.rootCauseSelectedGroup === 'all' ? null : this.rootCauseSelectedGroup,\n            viewBy: this.rootCauseViewBy\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.status_res === 'success') {\n          // Transform the API data to chart format\n          if (this.rootCauseViewBy === \"rootCause\") {\n            this.rootCauseTitle = 'Root Cause Analysis';\n          } else if (this.rootCauseViewBy === \"vintage\") {\n            this.rootCauseTitle = 'Vintage Code Analysis';\n          } else if (this.rootCauseViewBy === \"sector\") {\n            this.rootCauseTitle = 'Sector Analysis';\n          } else if (this.rootCauseViewBy === \"supplier\") {\n            this.rootCauseTitle = 'Supplier Code Analysis';\n          } else if (this.rootCauseViewBy === \"partNum\") {\n            this.rootCauseTitle = 'Part Number Analysis';\n          }\n          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);\n          console.log('Root cause chart data loaded:', data.categoryData);\n          console.log('Chart data length:', this.rootCauseChartData.length);\n          console.log('Sample data point:', this.rootCauseChartData[0]);\n          console.log('Breakout groups:', data.breakoutGroups);\n          console.log('Part numbers:', data.partNumbers);\n\n          console.log('Chart data loaded, watcher should handle update');\n        } else {\n          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);\n          // Fall back to mock data\n          this.rootCauseChartData = this.generateMockRootCauseData();\n          console.log('Using mock data:', this.rootCauseChartData.length);\n        }\n      } catch (error) {\n        console.error('Error loading root cause data:', error);\n        // Fall back to mock data\n        this.rootCauseChartData = this.generateMockRootCauseData();\n      } finally {\n        this.isRootCauseDataLoading = false;\n      }\n    },\n\n    handleRootCauseBarClick(event) {\n      if (event && event.data) {\n        const clickedData = event.data;\n        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);\n\n        // You can implement additional functionality here, such as showing more details\n        // or navigating to the MetisXFactors Group tab with this specific category\n        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\\nFail Rate: ${clickedData.value}%`);\n      }\n    },\n\n    transformRootCauseData(categoryData) {\n      // Transform the API response into chart format\n      const chartData = [];\n\n      // categoryData structure: { \"category\": { \"2024-03\": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }\n      for (const [category, monthData] of Object.entries(categoryData)) {\n        // Clean up category name (trim whitespace)\n        const cleanCategory = category.trim();\n        console.log(\"trim cat\", cleanCategory)\n\n        for (const [month, data] of Object.entries(monthData)) {\n          chartData.push({\n            group: cleanCategory,\n            key: month,\n            value: parseFloat(data.failRate.toFixed(2))\n          });\n        }\n      }\n\n      console.log('Transformed root cause data:', chartData);\n      console.log('Unique groups in data:', [...new Set(chartData.map(d => d.group))]);\n      console.log('Unique keys in data:', [...new Set(chartData.map(d => d.key))]);\n      console.log('Value range:', Math.min(...chartData.map(d => d.value)), 'to', Math.max(...chartData.map(d => d.value)));\n      return chartData;\n    },\n\n    generateMockRootCauseData() {\n      // Use the actual breakout groups for this PQE owner instead of generic categories\n      let categories = [];\n\n      if (this.breakoutGroups && this.breakoutGroups.length > 0) {\n        // Use the actual breakout groups for this PQE owner\n        categories = this.breakoutGroups.map(group => group.name);\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      } else {\n        // Fallback to sample data if breakout groups aren't loaded yet\n        if (this.pqeOwner === 'Albert G.') {\n          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];\n        } else if (this.pqeOwner === 'Sarah L.') {\n          categories = ['Stellar Core', 'Nebula Drive'];\n        } else {\n          categories = ['Sample Group 1', 'Sample Group 2'];\n        }\n\n        // If a specific group is selected, filter to only that group\n        if (this.rootCauseSelectedGroup !== 'all') {\n          categories = categories.filter(category => category === this.rootCauseSelectedGroup);\n        }\n      }\n\n      // Generate months based on selected time range\n      const now = new Date();\n      const months = [];\n      const monthsToGenerate = this.rootCauseTimeRange === '3month' ? 3 : 6;\n\n      for (let i = monthsToGenerate - 1; i >= 0; i--) {\n        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        months.push(`${year}-${month}`);\n      }\n\n      const data = [];\n\n      months.forEach(month => {\n        categories.forEach(category => {\n          // Generate realistic fail rate data (0-5% range)\n          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%\n          const variation = (Math.random() - 0.5) * 1; // ±0.5%\n          const failRate = Math.max(0.1, baseRate + variation);\n\n          data.push({\n            group: category,\n            key: month,\n            value: parseFloat(failRate.toFixed(2))\n          });\n        });\n      });\n\n      return data;\n    },\n\n    async load_line_chart() {\n      try {\n\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_fail_count_by_category_db\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({ PN: this.selectedPN, category: this.selectedCategory, area: this.selectedArea }),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          // Use full_data if available (Phase1B format), otherwise use counts_by_period (legacy format)\n          if (data.full_data && data.full_data.length > 0) {\n            console.log(\"Using full data format:\", data.full_data);\n            // Transform full data to chart format\n            this.line_chart_data = this.transformFullDataToChartFormat(data.full_data);\n          } else {\n            console.log(\"Using legacy counts_by_period format:\", data.counts_by_period);\n            this.line_chart_data = data.counts_by_period;\n          }\n          console.log(\"Line chart data:\", this.line_chart_data);\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }finally {\n        this.loading = false;  // Set loading to false once data is loaded\n      }\n    },\n\n    async load_bar_chart() {\n      try {\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_fail_count_by_category_db\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({PN: this.selectedPN, category: this.selectedCategory, area: this.selectedArea, startDate: this.selectedStartDate, endDate: this.selectedEndDate}),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          // Use full_data if available (Phase1B format), otherwise use counts_by_period (legacy format)\n          if (data.full_data && data.full_data.length > 0) {\n            console.log(\"Using full data format for bar chart:\", data.full_data);\n            // Transform full data to chart format\n            const transformedData = this.transformFullDataToChartFormat(data.full_data);\n            this.bar_chart_data = transformedData;\n            this.line_chart_data = transformedData;\n          } else {\n            console.log(\"Using legacy counts_by_period format for bar chart:\", data.counts_by_period);\n            this.bar_chart_data = data.counts_by_period;\n            this.line_chart_data = data.counts_by_period;\n          }\n          console.log(\"Bar chart data:\", this.bar_chart_data);\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }finally {\n        this.loading = false;  // Set loading to false once data is loaded\n      }\n    },\n\n    async get_categories() {\n      try {\n        let user_type = this.$store.getters.getUser_type;\n        let action = \"view\";\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unique_categories_db\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({ \"PN\": this.selectedPN, user_type, action }),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          this.categoryOptions = data.all_categories;\n          this.areaOptions = data.all_stages;\n          console.log(this.areaOptions)\n          console.log(\"Received data:\", data);\n\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }\n    },\n\n    async get_PNs() {\n      try {\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unique_pns\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({}),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          this.pnOptions = data.all_pns;\n\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }\n    },\n\n    async get_categories2(changedVar) {\n      try {\n        let user_type = this.$store.getters.getUser_type;\n        let action = \"view\";\n        let area = null;\n        let category = null;\n\n        if (changedVar === \"area\"){\n          area = this.selectedArea\n        }else if (changedVar === \"category\"){\n          category = this.selectedCategory\n        }\n        let token = this.$store.getters.getToken;\n        console.log(\"TOKEN\", token)\n        // Fetch data from the API\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_unique_categories_db2\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({ \"PN\": this.selectedPN, \"area\": area, \"category\": category, user_type, action }),\n        });\n\n        if (response.status === 401) {\n          console.error(\"Unauthorized: Check your token or credentials.\");\n        }\n\n        const data = await this.handleResponse(response);\n\n        if (data.status_res === \"success\") {\n          if (changedVar === \"area\"){\n            this.categoryOptions = data.new_array;\n          } else if (changedVar === \"category\"){\n            this.areaOptions = data.new_array;\n          }\n\n          console.log(\"Received data:\", data);\n\n        }\n      } catch (error) {\n        console.error(\"Error loading data:\", error);\n      }\n    },\n\n    handleRootCauseViewByChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseTimeRangeChange() {\n      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);\n      this.loadRootCauseData();\n    },\n\n    handleRootCauseGroupChange() {\n      console.log(`Root cause group changed to: ${this.rootCauseSelectedGroup}`);\n      console.log('Current chart data length before reload:', this.rootCauseChartData.length);\n      this.loadRootCauseData();\n    },\n\n    getAuthConfig() {\n      // Get authentication token from localStorage or Vuex store\n      const token = localStorage.getItem('token') || this.$store.state.token;\n\n      return {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      };\n    },\n\n    handleResponse(response) {\n      if (!response.ok) {\n        if (response.status === 401) {\n          this.session_expired_visible = true;\n        }\n      } else {\n        return response.json();\n      }\n    },\n\n    handleBarClick(data) {\n      // Update the chart data to only show the clicked bar\n      console.log(\"Bar data received:\", data);\n      // this.bar_chart_data = [data];\n    },\n\n    handlePointClick(data) {\n      console.log(\"Point clicked:\", data);\n      // Navigate to a new page or display a message\n      this.$router.push({ name: 'HelloPage' });\n    },\n\n    handleQueryMethodChange(newVal) {\n      // console.log(\"Query method changed:\", newVal);\n      this.queryMethod = newVal;\n      this.clearResults();\n    },\n\n    handleQueryTypeChange(newVal) {\n      // console.log(\"Query method changed:\", newVal);\n      this.queryType = newVal;\n      this.clearResults();\n    },\n\n    handleSubmit() {\n      this.loading = true;\n      this.aiQuery = '';\n      this.bar_chart_data = [];\n      this.line_chart_data = [];\n      console.log('PN:', this.selectedPN);\n      console.log('Area:', this.selectedArea);\n      console.log('Category:', this.selectedCategory);\n      console.log('Chart Type:', this.selectedChartType);\n      console.log(`Date Range: Start: ${this.selectedDate}`, );\n      this.selectedStartDate = this.selectedDateRange.startDate;\n      this.selectedEndDate = this.selectedDateRange.endDate;\n      // Update chart data based on selected chart type\n      if (this.selectedChartType === 'Bar') {\n        //this.chartData = this.bar_chart_data;\n        this.displayChartType = 'Bar';\n        this.load_bar_chart()\n      } else if (this.selectedChartType === 'Line') {\n        // this.chartData = this.line_chart_data;\n        this.displayChartType = 'Line';\n        this.load_line_chart()\n      }\n    },\n\n    async loadSuppliers() {\n      try {\n        const token = this.$store.getters.getToken;\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_suppliers\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({}),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data.status_res === \"success\") {\n          this.supplierOptions = data.suppliers.map(supplier => ({\n            label: supplier,\n            value: supplier\n          }));\n        }\n      } catch (error) {\n        console.error(\"Error loading suppliers:\", error);\n      }\n    },\n\n    async loadVintages() {\n      try {\n        const token = this.$store.getters.getToken;\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_vintages\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({}),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data.status_res === \"success\") {\n          this.vintageOptions = data.vintages.map(vintage => ({\n            label: vintage,\n            value: vintage\n          }));\n        }\n      } catch (error) {\n        console.error(\"Error loading vintages:\", error);\n      }\n    },\n\n    async loadGroups() {\n      try {\n        const token = this.$store.getters.getToken;\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_groups\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({}),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data.status_res === \"success\") {\n          this.groupOptions = data.groups.map(group => ({\n            label: group,\n            value: group\n          }));\n        }\n      } catch (error) {\n        console.error(\"Error loading groups:\", error);\n      }\n    },\n\n    async loadSectors() {\n      try {\n        const token = this.$store.getters.getToken;\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_sectors\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({}),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data.status_res === \"success\") {\n          this.sectorOptions = data.sectors.map(sector => ({\n            label: sector,\n            value: sector\n          }));\n        }\n      } catch (error) {\n        console.error(\"Error loading sectors:\", error);\n      }\n    },\n\n    handleViewChange() {\n      // Clear previous selections when view changes\n      this.selectedGroups = [];\n      this.selectedPartNumbers = [];\n      this.selectedSectors = [];\n      this.selectedVintages = [];\n      this.selectedSuppliers = [];\n    },\n\n    async handleComboSubmit() {\n      if (!this.comboStartDate || !this.comboEndDate) {\n        alert(\"Please select both start and end dates\");\n        return;\n      }\n\n      if (this.selectedViews.length === 0) {\n        alert(\"Please select at least one view option\");\n        return;\n      }\n\n      this.loading = true;\n      this.displayChartType = 'Combo';\n      this.combo_chart_data = [];\n      this.combo_table_data = [];\n\n      try {\n        const token = this.$store.getters.getToken;\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_combo_defrate_data\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({\n            startDate: this.comboStartDate,\n            endDate: this.comboEndDate,\n            viewBy: this.selectedViews.map(v => v.value),\n            groups: this.selectedGroups.map(g => g.value),\n            partNumbers: this.selectedPartNumbers.map(pn => pn.value),\n            sectors: this.selectedSectors.map(s => s.value),\n            vintages: this.selectedVintages.map(v => v.value),\n            suppliers: this.selectedSuppliers.map(s => s.value)\n          }),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data.status_res === \"success\") {\n          this.combo_chart_data = data.chart_data;\n          this.combo_table_data = data.table_data;\n        }\n      } catch (error) {\n        console.error(\"Error loading combo data:\", error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    async handleCompareSubmit() {\n      if (!this.comparePN1 || !this.comparePN2) {\n        alert(\"Please select both part numbers for comparison\");\n        return;\n      }\n\n      this.loading = true;\n      this.displayChartType = 'Compare';\n      this.compare_chart_data = [];\n      this.comparison_summary = [];\n\n      try {\n        const token = this.$store.getters.getToken;\n        const response = await fetch(process.env.VUE_APP_API_PATH + \"get_compare_data\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + token,\n          },\n          body: JSON.stringify({\n            partNumber1: this.comparePN1,\n            partNumber2: this.comparePN2,\n            startDate: this.compareDateRange.startDate,\n            endDate: this.compareDateRange.endDate\n          }),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data.status_res === \"success\") {\n          this.compare_chart_data = data.chart_data;\n\n          // Process comparison summary\n          this.comparison_summary = data.comparison_data.map(item => ({\n            partNumber: item.partNumber,\n            totalDefects: item.totalDefects,\n            topRootCause: Object.keys(item.rootCauses).reduce((a, b) =>\n              item.rootCauses[a] > item.rootCauses[b] ? a : b\n            )\n          }));\n        }\n      } catch (error) {\n        console.error(\"Error loading comparison data:\", error);\n      } finally {\n        this.loading = false;\n      }\n    },\n  }\n};\n</script>\n"]}]}