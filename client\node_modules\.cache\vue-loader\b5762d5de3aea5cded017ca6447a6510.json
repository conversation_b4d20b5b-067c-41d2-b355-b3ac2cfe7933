{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue", "mtime": 1756997497196}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Phase1.vue"], "names": [], "mappings": ";AAqPA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Phase1.vue", "sourceRoot": "src/views/Phase1", "sourcesContent": ["<template>\n  <cv-grid class=\"phase1-grid\">\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" @panel-toggle=\"toggleSideNav\" />\n    \n    <cv-row class=\"page-header\">\n      <cv-column>\n        <h1 class=\"page-title\">Phase 1 Analysis</h1>\n      </cv-column>\n    </cv-row>\n\n    <!-- Control Panel -->\n     <div class=\"control-pane\">\n    <cv-row class=\"control-panel\">\n      <cv-column :lg=\"12\" :md=\"8\" :sm=\"4\">\n        <cv-tile class=\"controls-tile\"\n        >\n          <div class=\"controls-header\">\n            <!-- Query Method Selection -->\n            <cv-radio-group\n              v-model=\"queryMethod\"\n              @change=\"handleQueryMethodChange\"\n              class=\"query-method-group\"\n            >\n              <cv-radio-button\n                name=\"query-method\"\n                label=\"Dropdown Builder\"\n                value=\"dropdown\"\n              />\n              <cv-radio-button\n                name=\"query-method\"\n                label=\"AI Prompt\"\n                value=\"ai\"\n              />\n            </cv-radio-group>\n          </div>\n\n          <!-- Dropdown Query Builder -->\n          <div v-if=\"queryMethod === 'dropdown'\" class=\"dropdown-builder\">\n            <div class=\"filter-row\">\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">View By:</label>\n                <cv-dropdown\n                  \n                  v-model=\"viewBy\"\n                  @change=\"handleViewByChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n                  <cv-dropdown-item value=\"vintage\">Vintage</cv-dropdown-item>\n                  <cv-dropdown-item value=\"sector\">Sector</cv-dropdown-item>\n                  <cv-dropdown-item value=\"supplier\">Supplier</cv-dropdown-item>\n                  <cv-dropdown-item value=\"partNum\">Part Number</cv-dropdown-item>\n                  <cv-dropdown-item value=\"category\">Category</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Time Range:</label>\n                <cv-dropdown\n                  v-model=\"timeRange\"\n                  @change=\"handleTimeRangeChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"1month\">1 Month</cv-dropdown-item>\n                  <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"12month\">12 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"custom\">Custom Range</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Process:</label>\n                <cv-dropdown\n                  v-model=\"selectedProcess\"\n                  @change=\"handleProcessChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"all\">All Processes</cv-dropdown-item>\n                  <cv-dropdown-item value=\"FAB\">FAB</cv-dropdown-item>\n                  <cv-dropdown-item value=\"FUL\">FUL</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Part Group:</label>\n                <cv-dropdown\n                  v-model=\"selectedPartGroup\"\n                  @change=\"handlePartGroupChange\"\n                  class=\"filter-dropdown\"\n                  :disabled=\"!partGroupOptions.length\"\n                >\n                  <cv-dropdown-item value=\"all\">All Part Groups</cv-dropdown-item>\n                  <cv-dropdown-item\n                    v-for=\"group in partGroupOptions\"\n                    :key=\"group.value\"\n                    :value=\"group.value\"\n                  >\n                    {{ group.label }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\" v-if=\"timeRange === 'custom'\">\n                <label class=\"filter-label\">Date Range:</label>\n                <cv-date-picker\n                  v-model=\"customDateRange\"\n                  label=\"Select Date Range\"\n                  kind=\"range\"\n                  :cal-options=\"calOptions\"\n                  placeholder=\"yyyy-mm-dd\"\n                  @change=\"handleDateRangeChange\"\n                />\n              </div>\n\n              <div class=\"filter-group action-group\">\n                <cv-button\n                  @click=\"executeDropdownQuery\"\n                  :disabled=\"isLoading\"\n                  class=\"execute-button\"\n                  kind=\"primary\"\n                >\n                  {{ isLoading ? 'Loading...' : 'Execute Query' }}\n                </cv-button>\n              </div>\n            </div>\n          </div>\n\n\n          <!-- Action Buttons -->\n          <div v-if=\"hasResults\" class=\"action-section\">\n            <h5 class=\"action-title\">Actions</h5>\n            <div class=\"action-buttons\">\n              <cv-button\n                @click=\"saveJob\"\n                :disabled=\"isLoading\"\n                class=\"action-button\"\n                kind=\"secondary\"\n                size=\"sm\"\n              >\n                Save Job\n              </cv-button>\n              <cv-button\n                @click=\"exportData('csv')\"\n                :disabled=\"isLoading || !chartData.length\"\n                class=\"action-button\"\n                kind=\"tertiary\"\n                size=\"sm\"\n              >\n                Export CSV\n              </cv-button>\n              <cv-button\n                @click=\"exportData('pdf')\"\n                :disabled=\"isLoading || !chartData.length\"\n                class=\"action-button\"\n                kind=\"tertiary\"\n                size=\"sm\"\n              >\n                Export PDF\n              </cv-button>\n            </div>\n          </div>\n        </cv-tile>\n      </cv-column>\n    </cv-row>\n    </div>\n    <div class=\"results-pane\">\n    <!-- Results Section -->\n    <cv-row class=\"results-section\" v-if=\"hasResults\">\n      <cv-column>\n        <cv-tile class=\"results-tile\">\n          <div class=\"results-header\">\n            <h4 class=\"results-title\">{{ resultsTitle }}</h4>\n            <div class=\"results-meta\" v-if=\"!isLoading && chartData.length > 0\">\n              <span class=\"data-count\">{{ chartData.length }} data points</span>\n              <span class=\"query-time\">{{ queryExecutionTime }}</span>\n            </div>\n          </div>\n\n          <!-- Loading State -->\n          <div v-if=\"isLoading\" class=\"loading-section\">\n            <cv-loading />\n            <p>Processing your query...</p>\n          </div>\n\n          <!-- Chart Display -->\n          <div v-else-if=\"chartData.length > 0\" class=\"chart-section\">\n            <div class=\"chart-container\">\n              <BarChart\n                v-if=\"chartType === 'bar'\"\n                :data=\"chartData\"\n                @bar-clicked=\"handleBarClick\"\n                :loading=\"false\"\n                class=\"chart-component\"\n              />\n              <LineChart\n                v-if=\"chartType === 'line'\"\n                :data=\"chartData\"\n                @point-clicked=\"handlePointClick\"\n                :loading=\"false\"\n                class=\"chart-component\"\n              />\n            </div>\n\n            <!-- Data Table -->\n            <div class=\"data-table-section\">\n              <h5>Data Summary</h5>\n              <cv-data-table\n                :columns=\"tableColumns\"\n                :data=\"tableData\"\n                :pagination=\"{ numberOfItems: chartData.length }\"\n                class=\"results-table\"\n              >\n                <template v-slot:cell=\"{ cell }\">\n                  <div v-if=\"cell.header === 'value'\" class=\"value-cell\">\n                    {{ formatValue(cell.value) }}\n                  </div>\n                  <div v-else>\n                    {{ cell.value }}\n                  </div>\n                </template>\n              </cv-data-table>\n            </div>\n          </div>\n\n          <!-- AI Response Display -->\n          <div v-if=\"aiResponse\" class=\"ai-response-section\">\n            <h5>AI Analysis:</h5>\n            <div class=\"ai-response-content\">\n              {{ aiResponse }}\n            </div>\n          </div>\n\n          <!-- No Results Message -->\n          <div v-else-if=\"!isLoading\" class=\"no-results\">\n            <p>No data found for the current query. Try adjusting your filters or prompt.</p>\n          </div>\n        </cv-tile>\n      </cv-column>\n    </cv-row>\n    </div>\n  </cv-grid>\n</template>\n\n<script>\nimport MainHeader from '../../components/MainHeader';\nimport BarChart from '../../components/BarChart';\nimport LineChart from '../../components/LineChart';\n\nexport default {\n  name: 'Phase1Page',\n  components: {\n    MainHeader,\n    BarChart,\n    LineChart\n  },\n  data() {\n    return {\n      // UI State\n      expandedSideNav: false,\n      useFixed: true,\n      isLoading: false,\n      hasResults: false,\n\n      // Query Method\n      queryMethod: 'dropdown',\n\n      // Dropdown Query Builder\n      viewBy: 'rootCause',\n      timeRange: '3month',\n      customDateRange: '',\n      selectedProcess: 'all',\n      selectedPartGroup: 'all',\n      partGroupOptions: [],\n\n      // AI Prompt\n      aiPrompt: '',\n      aiResponse: '',\n\n      // Results\n      chartData: [],\n      chartType: 'bar',\n      resultsTitle: '',\n      queryExecutionTime: '',\n\n      // Table Data\n      tableColumns: [\n        { header: 'Category', key: 'group' },\n        { header: 'Value', key: 'value' },\n        { header: 'Details', key: 'details' }\n      ],\n\n      // Current Job Data\n      currentJobData: null,\n\n      // Options\n      calOptions: { dateFormat: \"Y-m-d\" }\n    };\n  },\n  computed: {\n    tableData: function() {\n      var self = this;\n      return self.chartData.map(function(item, index) {\n        return {\n          id: index,\n          group: item.group,\n          value: item.value,\n          details: item.details || 'N/A'\n        };\n      });\n    }\n  },\n  mounted() {\n    this.loadPartGroupOptions();\n  },\n  methods: {\n    toggleSideNav: function() {\n      this.expandedSideNav = !this.expandedSideNav;\n    },\n\n    handleQueryMethodChange(newVal) {\n      // console.log(\"Query method changed:\", newVal);\n      this.queryMethod = newVal;\n      this.clearResults();\n    },\n\n    handleViewByChange: function() {\n      // Auto-execute if we have enough parameters\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleTimeRangeChange: function() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleDateRangeChange: function() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleProcessChange: function() {\n      this.loadPartGroupOptions();\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handlePartGroupChange: function() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    loadPartGroupOptions: function() {\n      var self = this;\n      var token = self.$store.getters.getToken;\n\n      fetch('/api-statit2/get_phase1_part_groups', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: 'Bearer ' + token,\n        },\n        body: JSON.stringify({\n          process: self.selectedProcess\n        }),\n      })\n      .then(function(response) {\n        return self.handleResponse(response);\n      })\n      .then(function(data) {\n        if (data && data.status_res === 'success') {\n          self.partGroupOptions = data.part_groups || [];\n        }\n      })\n      .catch(function(error) {\n        console.error('Error loading part group options:', error);\n      });\n    },\n\n    executeDropdownQuery: function() {\n      var self = this;\n      if (self.isLoading) return;\n\n      self.isLoading = true;\n      self.hasResults = true;\n      var startTime = Date.now();\n\n      var token = self.$store.getters.getToken;\n      var queryParams = {\n        viewBy: self.viewBy,\n        timeRange: self.timeRange,\n        customDateRange: self.timeRange === 'custom' ? self.customDateRange : null,\n        process: self.selectedProcess,\n        partGroup: self.selectedPartGroup\n      };\n\n      fetch('/api-statit2/execute_phase1_dropdown_query', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: 'Bearer ' + token,\n        },\n        body: JSON.stringify(queryParams),\n      })\n      .then(function(response) {\n        return self.handleResponse(response);\n      })\n      .then(function(data) {\n        if (data && data.status_res === 'success') {\n          self.chartData = data.chart_data || [];\n          self.chartType = data.chart_type || 'bar';\n          self.resultsTitle = data.title || 'Analysis Results';\n          self.aiResponse = '';\n\n          // Store current job data\n          self.currentJobData = {\n            type: 'dropdown',\n            params: queryParams,\n            results: data,\n            timestamp: new Date().toISOString()\n          };\n        }\n      })\n      .catch(function(error) {\n        console.error('Error executing dropdown query:', error);\n      })\n      .finally(function() {\n        self.isLoading = false;\n        var endTime = Date.now();\n        self.queryExecutionTime = 'Executed in ' + (endTime - startTime) + 'ms';\n      });\n    },\n\n    executeAiQuery: function() {\n      var self = this;\n      if (self.isLoading || !self.aiPrompt.trim()) return;\n\n      self.isLoading = true;\n      self.hasResults = true;\n      var startTime = Date.now();\n\n      var token = self.$store.getters.getToken;\n\n      fetch('/api-statit2/execute_phase1_ai_query', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: 'Bearer ' + token,\n        },\n        body: JSON.stringify({\n          prompt: self.aiPrompt.trim()\n        }),\n      })\n      .then(function(response) {\n        return self.handleResponse(response);\n      })\n      .then(function(data) {\n        if (data && data.status_res === 'success') {\n          self.chartData = data.chart_data || [];\n          self.chartType = data.chart_type || 'bar';\n          self.resultsTitle = data.title || 'AI Analysis Results';\n          self.aiResponse = data.ai_response || '';\n\n          // Store current job data\n          self.currentJobData = {\n            type: 'ai',\n            params: { prompt: self.aiPrompt.trim() },\n            results: data,\n            timestamp: new Date().toISOString()\n          };\n        }\n      })\n      .catch(function(error) {\n        console.error('Error executing AI query:', error);\n      })\n      .finally(function() {\n        self.isLoading = false;\n        var endTime = Date.now();\n        self.queryExecutionTime = 'Executed in ' + (endTime - startTime) + 'ms';\n      });\n    },\n\n    clearAiPrompt: function() {\n      this.aiPrompt = '';\n      this.clearResults();\n    },\n\n    clearResults: function() {\n      this.chartData = [];\n      this.aiResponse = '';\n      this.hasResults = false;\n      this.resultsTitle = '';\n    },\n\n    handleResponse: function(response) {\n      if (!response.ok) {\n        if (response.status === 401) {\n          // Handle session expiration\n          this.$router.push('/');\n        }\n        throw new Error('HTTP error! status: ' + response.status);\n      }\n      return response.json();\n    },\n\n    handleBarClick: function(data) {\n      console.log('Bar clicked:', data);\n      // Handle bar chart interactions\n    },\n\n    handlePointClick: function(data) {\n      console.log('Point clicked:', data);\n      // Handle line chart interactions\n    },\n\n    formatValue: function(value) {\n      if (typeof value === 'number') {\n        return value.toLocaleString();\n      }\n      return value;\n    },\n\n    exportData: function(format) {\n      var self = this;\n      if (!self.chartData.length) return;\n\n      var token = self.$store.getters.getToken;\n      var exportData = {\n        format: format,\n        data: self.chartData,\n        title: self.resultsTitle,\n        queryParams: (self.currentJobData && self.currentJobData.params) || {},\n        timestamp: new Date().toISOString()\n      };\n\n      fetch('/api-statit2/export_phase1_data', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: 'Bearer ' + token,\n        },\n        body: JSON.stringify(exportData),\n      })\n      .then(function(response) {\n        if (response.ok) {\n          return response.blob();\n        }\n        throw new Error('Export failed');\n      })\n      .then(function(blob) {\n        var url = window.URL.createObjectURL(blob);\n        var a = document.createElement('a');\n        a.href = url;\n        a.download = 'phase1_analysis_' + Date.now() + '.' + format;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n      })\n      .catch(function(error) {\n        console.error('Error exporting data:', error);\n      });\n    },\n\n    saveJob: function() {\n      var self = this;\n      if (!self.currentJobData) return;\n\n      var jobName = prompt('Enter a name for this job:');\n      if (!jobName) return;\n\n      var token = self.$store.getters.getToken;\n      var saveData = {\n        name: jobName,\n        jobData: self.currentJobData,\n        createdAt: new Date().toISOString()\n      };\n\n      fetch('/api-statit2/save_phase1_job', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: 'Bearer ' + token,\n        },\n        body: JSON.stringify(saveData),\n      })\n      .then(function(response) {\n        return self.handleResponse(response);\n      })\n      .then(function(data) {\n        if (data && data.status_res === 'success') {\n          alert('Job \"' + jobName + '\" saved successfully!');\n          // Navigate to saved reports tab\n          self.$router.push('/saved-reports');\n        }\n      })\n      .catch(function(error) {\n        console.error('Error saving job:', error);\n        alert('Error saving job. Please try again.');\n      });\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../styles/carbon-utils\";\n\n.control-pane {\n  position: sticky;   // stays pinned at top\n  top: 0;             // stick to the very top\n  z-index: 10;\n  width: 100%;        // ensures it stays above results\n}\n\n.results-pane {\n  margin-top: $spacing-05;\n}\n\n.phase1-grid {\n  min-height: 100vh;\n  padding: 0 $spacing-05;\n}\n\n.page-header {\n  margin-top: $spacing-05;\n  margin-bottom: $spacing-04;\n}\n\n.page-title {\n  @include carbon--type-style('productive-heading-03');\n\n  margin-bottom: $spacing-02;\n}\n\n.page-description {\n  @include carbon--type-style('body-short-01');\n  color: $text-02;\n}\n\n.control-panel {\n  margin-bottom: $spacing-04;\n}\n\n.controls-tile {\n  padding: $spacing-04;\n  height: fit-content;\n  background-color: #262626;\n}\n\n.controls-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: $spacing-04;\n}\n\n.controls-title {\n  @include carbon--type-style('productive-heading-01');\n  // color: $text-01;\n  margin: 0;\n}\n\n.control-label {\n  @include carbon--type-style('label-01');\n  // color: $text-01;\n  display: block;\n  margin-bottom: $spacing-02;\n}\n\n.query-method-group {\n  display: flex;\n  gap: $spacing-04;\n}\n\n.action-section {\n  margin-top: $spacing-05;\n  padding-top: $spacing-04;\n  border-top: 1px solid #393939;\n}\n\n.action-title {\n  @include carbon--type-style('productive-heading-01');\n  color: $text-01;\n  margin-bottom: $spacing-03;\n}\n\n.action-buttons {\n  display: flex;\n  gap: $spacing-03;\n  flex-wrap: wrap;\n}\n\n.action-button {\n  min-width: 120px;\n}\n\n.dropdown-builder {\n  .filter-row {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\n    gap: $spacing-03;\n    margin-bottom: $spacing-04;\n  }\n\n  .filter-group {\n    min-width: 0;\n\n    &.action-group {\n      display: flex;\n      align-items: flex-end;\n      grid-column: span 2;\n    }\n  }\n\n  .filter-label {\n    @include carbon--type-style('label-01');\n    // color: $text-01;\n    display: block;\n    margin-bottom: $spacing-02;\n  }\n\n  .filter-dropdown {\n    width: 100%;\n  }\n}\n\n.ai-prompt-section {\n  .prompt-input-group {\n    margin-bottom: $spacing-03;\n  }\n\n  .prompt-textarea {\n    width: 100%;\n  }\n\n  .prompt-controls {\n    display: flex;\n    gap: $spacing-03;\n  }\n}\n\n.execute-button {\n  min-width: 120px;\n}\n\n.clear-button {\n  min-width: 80px;\n}\n\n.results-section {\n  margin-bottom: $spacing-05;\n}\n\n.results-tile {\n  padding: $spacing-04;\n  background-color: #262626;\n}\n\n.results-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: $spacing-04;\n}\n\n.results-title {\n  @include carbon--type-style('productive-heading-02');\n  color: $text-01;\n  margin: 0;\n}\n\n.results-meta {\n  display: flex;\n  gap: $spacing-04;\n\n  .data-count, .query-time {\n    @include carbon--type-style('caption-01');\n    color: $text-02;\n  }\n}\n\n.loading-section {\n  text-align: center;\n  padding: $spacing-05;\n\n  p {\n    @include carbon--type-style('body-short-01');\n    color: $text-02;\n    margin-top: $spacing-03;\n  }\n}\n\n.chart-section {\n  .chart-container {\n    height: 400px;\n    margin-bottom: $spacing-05;\n    background-color: #262626;\n    border-radius: $spacing-02;\n    padding: $spacing-04;\n  }\n\n  .chart-component {\n    height: 100%;\n    width: 100%;\n  }\n}\n\n.data-table-section {\n  h5 {\n    @include carbon--type-style('productive-heading-01');\n    color: $text-01;\n    margin-bottom: $spacing-03;\n  }\n\n  .results-table {\n    max-height: 300px;\n    overflow-y: auto;\n  }\n\n  .value-cell {\n    font-weight: 600;\n    color: $text-01;\n  }\n}\n\n.ai-response-section {\n  margin-top: $spacing-04;\n  padding: $spacing-04;\n  background-color: $ui-01;\n  border-radius: $spacing-02;\n\n  h5 {\n    @include carbon--type-style('productive-heading-01');\n    color: $text-01;\n    margin-bottom: $spacing-03;\n  }\n}\n\n.ai-response-content {\n  @include carbon--type-style('body-short-01');\n  color: $text-01;\n  line-height: 1.5;\n  white-space: pre-wrap;\n}\n\n.no-results {\n  text-align: center;\n  padding: $spacing-05;\n\n  p {\n    @include carbon--type-style('body-short-01');\n    color: $text-02;\n  }\n}\n</style>\n"]}]}