{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue?vue&type=template&id=2da97105&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue", "mtime": 1756997497196}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}